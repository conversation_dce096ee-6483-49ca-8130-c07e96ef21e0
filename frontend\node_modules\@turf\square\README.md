# @turf/square

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## square

Takes a bounding box and calculates the minimum square bounding box that
would contain the input.

**Parameters**

-   `bbox` **[BBox][1]** extent in [west, south, east, north] order

**Examples**

```javascript
var bbox = [-20, -20, -15, 0];
var squared = turf.square(bbox);

//addToMap
var addToMap = [turf.bboxPolygon(bbox), turf.bboxPolygon(squared)]
```

Returns **[BBox][1]** a square surrounding `bbox`

[1]: https://tools.ietf.org/html/rfc7946#section-5

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/square
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```

#!/bin/bash

# Script d'installation et configuration de tileserver-gl pour les tuiles du Maroc
# Respecte l'intégrité territoriale du Royaume du Maroc

set -e

echo "🇲🇦 Configuration du serveur de tuiles vectorielles - Royaume du Maroc"
echo "=================================================================="

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Répertoires
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TILES_DIR="$PROJECT_ROOT/tiles"
MBTILES_FILE="$TILES_DIR/maroc.mbtiles"

echo -e "${BLUE}📁 Répertoire du projet: $PROJECT_ROOT${NC}"
echo -e "${BLUE}📁 Répertoire des tuiles: $TILES_DIR${NC}"

# Créer le répertoire des tuiles s'il n'existe pas
mkdir -p "$TILES_DIR"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js n'est pas installé. Veuillez l'installer d'abord.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js détecté: $(node --version)${NC}"

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm n'est pas installé.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ npm détecté: $(npm --version)${NC}"

# Installer tileserver-gl globalement
echo -e "${YELLOW}📦 Installation de tileserver-gl...${NC}"
if ! command -v tileserver-gl &> /dev/null; then
    npm install -g tileserver-gl
    echo -e "${GREEN}✅ tileserver-gl installé avec succès${NC}"
else
    echo -e "${GREEN}✅ tileserver-gl déjà installé: $(tileserver-gl --version)${NC}"
fi

# Générer les tuiles du Maroc si elles n'existent pas
if [ ! -f "$MBTILES_FILE" ]; then
    echo -e "${YELLOW}🏗️ Génération des tuiles vectorielles du Maroc...${NC}"
    
    # Vérifier si Python est disponible
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ Python n'est pas installé. Nécessaire pour générer les tuiles.${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🐍 Python détecté: $($PYTHON_CMD --version)${NC}"
    
    # Exécuter le générateur de tuiles
    cd "$PROJECT_ROOT"
    $PYTHON_CMD scripts/generate_morocco_mbtiles.py "$MBTILES_FILE"
    
    if [ -f "$MBTILES_FILE" ]; then
        echo -e "${GREEN}✅ Tuiles générées avec succès: $MBTILES_FILE${NC}"
        echo -e "${BLUE}📊 Taille du fichier: $(du -h "$MBTILES_FILE" | cut -f1)${NC}"
    else
        echo -e "${RED}❌ Échec de la génération des tuiles${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Fichier de tuiles existant: $MBTILES_FILE${NC}"
fi

# Créer un fichier de configuration pour tileserver-gl
CONFIG_FILE="$TILES_DIR/config.json"
cat > "$CONFIG_FILE" << EOF
{
  "options": {
    "paths": {
      "root": "$TILES_DIR",
      "fonts": "fonts",
      "sprites": "sprites",
      "styles": "styles",
      "mbtiles": "$TILES_DIR"
    },
    "domains": ["localhost:8080"],
    "formatQuality": {
      "jpeg": 80,
      "webp": 90
    },
    "maxzoom": 18,
    "maxsize": 2048,
    "pbfAlias": "pbf",
    "serveAllFonts": false,
    "serveAllStyles": false,
    "tileMargin": 0
  },
  "styles": {
    "basic": {
      "style": "basic.json",
      "tilejson": {
        "type": "overlay",
        "attribution": "© Données géographiques Royaume du Maroc"
      }
    }
  },
  "data": {
    "maroc": {
      "mbtiles": "maroc.mbtiles"
    }
  }
}
EOF

echo -e "${GREEN}✅ Configuration créée: $CONFIG_FILE${NC}"

# Créer un style de base
STYLE_FILE="$TILES_DIR/basic.json"
cat > "$STYLE_FILE" << EOF
{
  "version": 8,
  "name": "Royaume du Maroc - Style de base",
  "metadata": {
    "mapbox:autocomposite": false,
    "mapbox:type": "template",
    "mapbox:groups": {}
  },
  "sources": {
    "maroc": {
      "type": "vector",
      "url": "mbtiles://maroc"
    }
  },
  "sprite": "",
  "glyphs": "",
  "layers": [
    {
      "id": "background",
      "type": "background",
      "paint": {
        "background-color": "#f8f8f8"
      }
    },
    {
      "id": "boundaries",
      "type": "line",
      "source": "maroc",
      "source-layer": "boundaries",
      "paint": {
        "line-color": "#d62728",
        "line-width": 2,
        "line-opacity": 1
      }
    },
    {
      "id": "cities",
      "type": "circle",
      "source": "maroc",
      "source-layer": "cities",
      "paint": {
        "circle-color": "#2196F3",
        "circle-radius": 4,
        "circle-stroke-color": "#ffffff",
        "circle-stroke-width": 1
      }
    }
  ]
}
EOF

echo -e "${GREEN}✅ Style de base créé: $STYLE_FILE${NC}"

# Créer un script de démarrage
START_SCRIPT="$TILES_DIR/start_server.sh"
cat > "$START_SCRIPT" << EOF
#!/bin/bash
echo "🚀 Démarrage du serveur de tuiles vectorielles du Maroc..."
echo "🌐 Serveur disponible sur: http://localhost:8080"
echo "📍 Style JSON: http://localhost:8080/styles/basic/style.json"
echo "🗺️ Tuiles PBF: http://localhost:8080/data/maroc/{z}/{x}/{y}.pbf"
echo ""
echo "Appuyez sur Ctrl+C pour arrêter le serveur"
echo ""

cd "$TILES_DIR"
tileserver-gl --config config.json --port 8080 --verbose
EOF

chmod +x "$START_SCRIPT"
echo -e "${GREEN}✅ Script de démarrage créé: $START_SCRIPT${NC}"

# Créer un script PowerShell pour Windows
START_SCRIPT_PS="$TILES_DIR/start_server.ps1"
cat > "$START_SCRIPT_PS" << EOF
# Script PowerShell pour démarrer le serveur de tuiles
Write-Host "🚀 Démarrage du serveur de tuiles vectorielles du Maroc..." -ForegroundColor Green
Write-Host "🌐 Serveur disponible sur: http://localhost:8080" -ForegroundColor Blue
Write-Host "📍 Style JSON: http://localhost:8080/styles/basic/style.json" -ForegroundColor Blue
Write-Host "🗺️ Tuiles PBF: http://localhost:8080/data/maroc/{z}/{x}/{y}.pbf" -ForegroundColor Blue
Write-Host ""
Write-Host "Appuyez sur Ctrl+C pour arrêter le serveur" -ForegroundColor Yellow
Write-Host ""

Set-Location "$TILES_DIR"
tileserver-gl --config config.json --port 8080 --verbose
EOF

echo -e "${GREEN}✅ Script PowerShell créé: $START_SCRIPT_PS${NC}"

echo ""
echo -e "${GREEN}🎉 Configuration terminée avec succès!${NC}"
echo ""
echo -e "${YELLOW}📋 Prochaines étapes:${NC}"
echo -e "${BLUE}1. Démarrer le serveur de tuiles:${NC}"
echo -e "   ${YELLOW}Linux/Mac:${NC} $START_SCRIPT"
echo -e "   ${YELLOW}Windows:${NC} powershell -ExecutionPolicy Bypass -File \"$START_SCRIPT_PS\""
echo ""
echo -e "${BLUE}2. Tester l'accès aux tuiles:${NC}"
echo -e "   ${YELLOW}Style JSON:${NC} http://localhost:8080/styles/basic/style.json"
echo -e "   ${YELLOW}Tuiles PBF:${NC} http://localhost:8080/data/maroc/{z}/{x}/{y}.pbf"
echo ""
echo -e "${BLUE}3. Intégrer dans React:${NC}"
echo -e "   Le composant OfflineMap.jsx est configuré pour utiliser ces URLs"
echo ""
echo -e "${GREEN}✅ Le serveur respecte l'intégrité territoriale du Royaume du Maroc${NC}"

// splaytree-ts v1.0.2 Copyright (c) 2022, <PERSON><PERSON>
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self)["splaytree-ts"]=t["splaytree-ts"]||{})}(this,(function(t){"use strict";class e{key;left=null;right=null;constructor(t){this.key=t}}class i extends e{constructor(t){super(t)}}class s extends e{value;constructor(t,e){super(t),this.value=e}replaceValue(t){const e=new s(this.key,t);return e.left=this.left,e.right=this.right,e}}class r{size=0;modificationCount=0;splayCount=0;splay(t){const e=this.root;if(null==e)return this.compare(t,t),-1;let i=null,s=null,r=null,o=null,l=e;const n=this.compare;let h;for(;;)if(h=n(l.key,t),h>0){let e=l.left;if(null==e)break;if(h=n(e.key,t),h>0&&(l.left=e.right,e.right=l,l=e,e=l.left,null==e))break;null==i?s=l:i.left=l,i=l,l=e}else{if(!(h<0))break;{let e=l.right;if(null==e)break;if(h=n(e.key,t),h<0&&(l.right=e.left,e.left=l,l=e,e=l.right,null==e))break;null==r?o=l:r.right=l,r=l,l=e}}return null!=r&&(r.right=l.left,l.left=o),null!=i&&(i.left=l.right,l.right=s),this.root!==l&&(this.root=l,this.splayCount++),h}splayMin(t){let e=t,i=e.left;for(;null!=i;){const t=i;e.left=t.right,t.right=e,e=t,i=e.left}return e}splayMax(t){let e=t,i=e.right;for(;null!=i;){const t=i;e.right=t.left,t.left=e,e=t,i=e.right}return e}_delete(t){if(null==this.root)return null;if(0!=this.splay(t))return null;let e=this.root;const i=e,s=e.left;if(this.size--,null==s)this.root=e.right;else{const t=e.right;e=this.splayMax(s),e.right=t,this.root=e}return this.modificationCount++,i}addNewRoot(t,e){this.size++,this.modificationCount++;const i=this.root;null!=i?(e<0?(t.left=i,t.right=i.right,i.right=null):(t.right=i,t.left=i.left,i.left=null),this.root=t):this.root=t}_first(){const t=this.root;return null==t?null:(this.root=this.splayMin(t),this.root)}_last(){const t=this.root;return null==t?null:(this.root=this.splayMax(t),this.root)}clear(){this.root=null,this.size=0,this.modificationCount++}has(t){return this.validKey(t)&&0==this.splay(t)}defaultCompare(){return(t,e)=>t<e?-1:t>e?1:0}wrap(){return{getRoot:()=>this.root,setRoot:t=>{this.root=t},getSize:()=>this.size,getModificationCount:()=>this.modificationCount,getSplayCount:()=>this.splayCount,setSplayCount:t=>{this.splayCount=t},splay:t=>this.splay(t),has:t=>this.has(t)}}}class o extends r{root=null;compare;validKey;constructor(t,e){super(),this.compare=t??this.defaultCompare(),this.validKey=e??(t=>null!=t&&null!=t)}delete(t){return!!this.validKey(t)&&null!=this._delete(t)}forEach(t){const e=new f(this.wrap());let i;for(;i=e.next(),!i.done;)t(i.value[1],i.value[0],this)}get(t){if(this.validKey(t)&&null!=this.root){if(0==this.splay(t))return this.root.value}}hasValue(t){const e=this.splayCount,i=s=>{for(;null!=s;){if(s.value==t)return!0;if(e!=this.splayCount)throw"Concurrent modification during iteration.";if(null!=s.right&&i(s.right))return!0;s=s.left}return!1};return i(this.root)}set(t,e){const i=this.splay(t);return 0==i?(this.root=this.root.replaceValue(e),this.splayCount+=1,this):(this.addNewRoot(new s(t,e),i),this)}setAll(t){t.forEach(((t,e)=>{this.set(e,t)}))}setIfAbsent(t,e){let i=this.splay(t);if(0==i)return this.root.value;const r=this.modificationCount,o=this.splayCount,l=e();if(r!=this.modificationCount)throw"Concurrent modification during iteration.";return o!=this.splayCount&&(i=this.splay(t)),this.addNewRoot(new s(t,l),i),l}isEmpty(){return null==this.root}isNotEmpty(){return!this.isEmpty()}firstKey(){return null==this.root?null:this._first().key}lastKey(){return null==this.root?null:this._last().key}lastKeyBefore(t){if(null==t)throw"Invalid arguments(s)";if(null==this.root)return null;if(this.splay(t)<0)return this.root.key;let e=this.root.left;if(null==e)return null;let i=e.right;for(;null!=i;)e=i,i=e.right;return e.key}firstKeyAfter(t){if(null==t)throw"Invalid arguments(s)";if(null==this.root)return null;if(this.splay(t)>0)return this.root.key;let e=this.root.right;if(null==e)return null;let i=e.left;for(;null!=i;)e=i,i=e.left;return e.key}update(t,e,i){let r=this.splay(t);if(0==r){const i=this.modificationCount,s=this.splayCount,r=e(this.root.value);if(i!=this.modificationCount)throw"Concurrent modification during iteration.";return s!=this.splayCount&&this.splay(t),this.root=this.root.replaceValue(r),this.splayCount+=1,r}if(null!=i){const e=this.modificationCount,o=this.splayCount,l=i();if(e!=this.modificationCount)throw"Concurrent modification during iteration.";return o!=this.splayCount&&(r=this.splay(t)),this.addNewRoot(new s(t,l),r),l}throw"Invalid argument (key): Key not in map."}updateAll(t){if(null==this.root)return;const e=new f(this.wrap());let i;for(;i=e.next(),!i.done;){const s=t(...i.value);e.replaceValue(s)}}keys(){return new h(this.wrap())}values(){return new a(this.wrap())}entries(){return this[Symbol.iterator]()}[Symbol.iterator](){return new f(this.wrap())}[Symbol.toStringTag]="[object Map]"}class l extends r{root=null;compare;validKey;constructor(t,e){super(),this.compare=t??this.defaultCompare(),this.validKey=e??(t=>null!=t&&null!=t)}delete(t){return!!this.validKey(t)&&null!=this._delete(t)}deleteAll(t){for(const e of t)this.delete(e)}forEach(t){const e=this[Symbol.iterator]();let i;for(;i=e.next(),!i.done;)t(i.value,i.value,this)}add(t){const e=this.splay(t);return 0!=e&&this.addNewRoot(new i(t),e),this}addAndReturn(t){const e=this.splay(t);return 0!=e&&this.addNewRoot(new i(t),e),this.root.key}addAll(t){for(const e of t)this.add(e)}isEmpty(){return null==this.root}isNotEmpty(){return null!=this.root}single(){if(0==this.size)throw"Bad state: No element";if(this.size>1)throw"Bad state: Too many element";return this.root.key}first(){if(0==this.size)throw"Bad state: No element";return this._first().key}last(){if(0==this.size)throw"Bad state: No element";return this._last().key}lastBefore(t){if(null==t)throw"Invalid arguments(s)";if(null==this.root)return null;if(this.splay(t)<0)return this.root.key;let e=this.root.left;if(null==e)return null;let i=e.right;for(;null!=i;)e=i,i=e.right;return e.key}firstAfter(t){if(null==t)throw"Invalid arguments(s)";if(null==this.root)return null;if(this.splay(t)>0)return this.root.key;let e=this.root.right;if(null==e)return null;let i=e.left;for(;null!=i;)e=i,i=e.left;return e.key}retainAll(t){const e=new l(this.compare,this.validKey),i=this.modificationCount;for(const s of t){if(i!=this.modificationCount)throw"Concurrent modification during iteration.";this.validKey(s)&&0==this.splay(s)&&e.add(this.root.key)}e.size!=this.size&&(this.root=e.root,this.size=e.size,this.modificationCount++)}lookup(t){if(!this.validKey(t))return null;return 0!=this.splay(t)?null:this.root.key}intersection(t){const e=new l(this.compare,this.validKey);for(const i of this)t.has(i)&&e.add(i);return e}difference(t){const e=new l(this.compare,this.validKey);for(const i of this)t.has(i)||e.add(i);return e}union(t){const e=this.clone();return e.addAll(t),e}clone(){const t=new l(this.compare,this.validKey);return t.size=this.size,t.root=this.copyNode(this.root),t}copyNode(t){if(null==t)return null;const e=new i(t.key);return function t(e,s){let r,o;do{if(r=e.left,o=e.right,null!=r){const e=new i(r.key);s.left=e,t(r,e)}if(null!=o){const t=new i(o.key);s.right=t,e=o,s=t}}while(null!=o)}(t,e),e}toSet(){return this.clone()}entries(){return new u(this.wrap())}keys(){return this[Symbol.iterator]()}values(){return this[Symbol.iterator]()}[Symbol.iterator](){return new h(this.wrap())}[Symbol.toStringTag]="[object Set]"}class n{tree;path=new Array;modificationCount=null;splayCount;constructor(t){this.tree=t,this.splayCount=t.getSplayCount()}[Symbol.iterator](){return this}next(){return this.moveNext()?{done:!1,value:this.current()}:{done:!0,value:null}}current(){if(!this.path.length)return null;const t=this.path[this.path.length-1];return this.getValue(t)}rebuildPath(t){this.path.splice(0,this.path.length),this.tree.splay(t),this.path.push(this.tree.getRoot()),this.splayCount=this.tree.getSplayCount()}findLeftMostDescendent(t){for(;null!=t;)this.path.push(t),t=t.left}moveNext(){if(this.modificationCount!=this.tree.getModificationCount()){if(null==this.modificationCount){this.modificationCount=this.tree.getModificationCount();let t=this.tree.getRoot();for(;null!=t;)this.path.push(t),t=t.left;return this.path.length>0}throw"Concurrent modification during iteration."}if(!this.path.length)return!1;this.splayCount!=this.tree.getSplayCount()&&this.rebuildPath(this.path[this.path.length-1].key);let t=this.path[this.path.length-1],e=t.right;if(null!=e){for(;null!=e;)this.path.push(e),e=e.left;return!0}for(this.path.pop();this.path.length&&this.path[this.path.length-1].right===t;)t=this.path.pop();return this.path.length>0}}class h extends n{getValue(t){return t.key}}class u extends n{getValue(t){return[t.key,t.key]}}class a extends n{constructor(t){super(t)}getValue(t){return t.value}}class f extends n{constructor(t){super(t)}getValue(t){return[t.key,t.value]}replaceValue(t){if(this.modificationCount!=this.tree.getModificationCount())throw"Concurrent modification during iteration.";this.splayCount!=this.tree.getSplayCount()&&this.rebuildPath(this.path[this.path.length-1].key);const e=this.path.pop(),i=e.replaceValue(t);if(this.path.length){const t=this.path[this.path.length-1];e===t.left?t.left=i:t.right=i}else this.tree.setRoot(i);this.path.push(i);const s=this.tree.getSplayCount()+1;this.tree.setSplayCount(s),this.splayCount=s}}t.SplayTreeMap=o,t.SplayTreeSet=l,t.version="1.0.2",Object.defineProperty(t,"__esModule",{value:!0})}));

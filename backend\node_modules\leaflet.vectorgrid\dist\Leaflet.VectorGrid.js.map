{"version": 3, "file": "Leaflet.VectorGrid.js", "sources": ["../.gobble-build/03-merge/1/bundle.js", "../.gobble-build/03-merge/1/Leaflet.Renderer.Canvas.Tile.js", "../.gobble-build/03-merge/1/Leaflet.VectorGrid.Slicer.js", "../.gobble-build/03-merge/1/Leaflet.VectorGrid.Protobuf.js", "../node_modules/vector-tile/index.js", "../node_modules/vector-tile/lib/vectortile.js", "../node_modules/vector-tile/lib/vectortilelayer.js", "../node_modules/vector-tile/lib/vectortilefeature.js", "../node_modules/point-geometry/index.js", "../node_modules/pbf/index.js", "../node_modules/ieee754/index.js", "../.gobble-build/03-merge/1/Leaflet.VectorGrid.js", "../.gobble-build/03-merge/1/Symbolizer.Fill.js", "../.gobble-build/03-merge/1/Symbolizer.Line.js", "../.gobble-build/03-merge/1/Symbolizer.Point.js", "../.gobble-build/03-merge/1/Symbolizer.PolyBase.js", "../.gobble-build/03-merge/1/Symbolizer.js", "../.gobble-build/03-merge/1/Leaflet.Renderer.SVG.Tile.js"], "sourcesContent": ["\n// Aux file to bundle everything together\n\nimport {} from './Leaflet.VectorGrid.js';\nimport {} from './Leaflet.VectorGrid.Protobuf.js';\nimport {} from './Leaflet.VectorGrid.Slicer.js';\nimport {} from './Leaflet.Renderer.Canvas.Tile.js';\nimport {} from './Leaflet.Renderer.SVG.Tile.js';\n\n", "\n\nL.Canvas.Tile = L.Canvas.extend({\n\n\tinitialize: function (tileCoord, tileSize, options) {\n\t\tL.Canvas.prototype.initialize.call(this, options);\n\t\tthis._tileCoord = tileCoord;\n\t\tthis._size = tileSize;\n\n\t\tthis._initContainer();\n\t\tthis._container.setAttribute('width', this._size.x);\n\t\tthis._container.setAttribute('height', this._size.y);\n\t\tthis._layers = {};\n\t\tthis._drawnLayers = {};\n\t\tthis._drawing = true;\n\n\t\tif (options.interactive) {\n\t\t\t// By default, Leaflet tiles do not have pointer events\n\t\t\tthis._container.style.pointerEvents = 'auto';\n\t\t}\n\t},\n\n\tgetCoord: function() {\n\t\treturn this._tileCoord;\n\t},\n\n\tgetContainer: function() {\n\t\treturn this._container;\n\t},\n\n\tgetOffset: function() {\n\t\treturn this._tileCoord.scaleBy(this._size).subtract(this._map.getPixelOrigin());\n\t},\n\n\tonAdd: L.Util.falseFn,\n\n\taddTo: function(map) {\n\t\tthis._map = map;\n\t},\n\n\tremoveFrom: function (map) {\n\t\tdelete this._map;\n\t},\n\n\t_onClick: function (e) {\n\t\tvar point = this._map.mouseEventToLayerPoint(e).subtract(this.getOffset()), layer, clickedLayer;\n\n\t\tfor (var id in this._layers) {\n\t\t\tlayer = this._layers[id];\n\t\t\tif (layer.options.interactive && layer._containsPoint(point) && !this._map._draggableMoved(layer)) {\n\t\t\t\tclickedLayer = layer;\n\t\t\t}\n\t\t}\n\t\tif (clickedLayer)  {\n\t\t\tL.DomEvent.fakeStop(e);\n\t\t\tthis._fireEvent([clickedLayer], e);\n\t\t}\n\t},\n\n\t_onMouseMove: function (e) {\n\t\tif (!this._map || this._map.dragging.moving() || this._map._animatingZoom) { return; }\n\n\t\tvar point = this._map.mouseEventToLayerPoint(e).subtract(this.getOffset());\n\t\tthis._handleMouseHover(e, point);\n\t},\n\n\t/// TODO: Modify _initPath to include an extra parameter, a group name\n\t/// to order symbolizers by z-index\n\n\t_updateIcon: function (layer) {\n\t\tif (!this._drawing) { return; }\n\n\t\tvar icon = layer.options.icon,\n\t\t    options = icon.options,\n\t\t    size = L.point(options.iconSize),\n\t\t    anchor = options.iconAnchor ||\n\t\t        \t size && size.divideBy(2, true),\n\t\t    p = layer._point.subtract(anchor),\n\t\t    ctx = this._ctx,\n\t\t    img = layer._getImage();\n\n\t\tif (img.complete) {\n\t\t\tctx.drawImage(img, p.x, p.y, size.x, size.y);\n\t\t} else {\n\t\t\tL.DomEvent.on(img, 'load', function() {\n\t\t\t\tctx.drawImage(img, p.x, p.y, size.x, size.y);\n\t\t\t});\n\t\t}\n\n\t\tthis._drawnLayers[layer._leaflet_id] = layer;\n\t}\n});\n\n\nL.canvas.tile = function(tileCoord, tileSize, opts){\n\treturn new L.Canvas.Tile(tileCoord, tileSize, opts);\n}\n\n", "\n// The geojson/topojson is sliced into tiles via a web worker.\n// This import statement depends on rollup-file-as-blob, so that the\n// variable 'workerCode' is a blob URL.\n\nimport workerCode from './slicerWebWorker.js.worker';\n\n/*\n * 🍂class VectorGrid.Slicer\n * 🍂extends VectorGrid\n *\n * A `VectorGrid` for slicing up big GeoJSON or TopoJSON documents in vector\n * tiles, leveraging [`geojson-vt`](https://github.com/mapbox/geojson-vt).\n *\n * 🍂example\n *\n * ```\n * var geoJsonDocument = {\n * \ttype: 'FeatureCollection',\n * \tfeatures: [ ... ]\n * };\n *\n * L.vectorGrid.slicer(geoJsonDocument, {\n * \tvectorTileLayerStyles: {\n * \t\tsliced: { ... }\n * \t}\n * }).addTo(map);\n *\n * ```\n *\n * `VectorGrid.Slicer` can also handle [TopoJSON](https://github.com/mbostock/topojson) transparently:\n * ```js\n * var layer = L.vectorGrid.slicer(topojson, options);\n * ```\n *\n * The TopoJSON format [implicitly groups features into \"objects\"](https://github.com/mbostock/topojson-specification/blob/master/README.md#215-objects).\n * These will be transformed into vector tile layer names when styling (the\n * `vectorTileLayerName` option is ignored when using TopoJSON).\n *\n */\n\nL.VectorGrid.Slicer = L.VectorGrid.extend({\n\n\toptions: {\n\t\t// 🍂section\n\t\t// Additionally to these options, `VectorGrid.Slicer` can take in any\n\t\t// of the [`geojson-vt` options](https://github.com/mapbox/geojson-vt#options).\n\n\t\t// 🍂option vectorTileLayerName: String = 'sliced'\n\t\t// Vector tiles contain a set of *data layers*, and those data layers\n\t\t// contain features. Thus, the slicer creates one data layer, with\n\t\t// the name given in this option. This is important for symbolizing the data.\n\t\tvectorTileLayerName: 'sliced',\n\n\t\textent: 4096,\t// Default for geojson-vt\n\t\tmaxZoom: 14  \t// Default for geojson-vt\n\t},\n\n\tinitialize: function(geojson, options) {\n\t\tL.VectorGrid.prototype.initialize.call(this, options);\n\n\t\t// Create a shallow copy of this.options, excluding things that might\n\t\t// be functions - we only care about topojson/geojsonvt options\n\t\tvar options = {};\n\t\tfor (var i in this.options) {\n\t\t\tif (i !== 'rendererFactory' &&\n\t\t\t\ti !== 'vectorTileLayerStyles' &&\n\t\t\t\ttypeof (this.options[i]) !== 'function'\n\t\t\t) {\n\t\t\t\toptions[i] = this.options[i];\n\t\t\t}\n\t\t}\n\n// \t\tthis._worker = new Worker(window.URL.createObjectURL(new Blob([workerCode])));\n\t\tthis._worker = new Worker(workerCode);\n\n\t\t// Send initial data to worker.\n\t\tthis._worker.postMessage(['slice', geojson, options]);\n\n\t},\n\n\n\t_getVectorTilePromise: function(coords) {\n\n\t\tvar _this = this;\n\n\t\tvar p = new Promise( function waitForWorker(res) {\n\t\t\t_this._worker.addEventListener('message', function recv(m) {\n\t\t\t\tif (m.data.coords &&\n\t\t\t\t    m.data.coords.x === coords.x &&\n\t\t\t\t    m.data.coords.y === coords.y &&\n\t\t\t\t    m.data.coords.z === coords.z ) {\n\n\t\t\t\t\tres(m.data);\n\t\t\t\t\t_this._worker.removeEventListener('message', recv);\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tthis._worker.postMessage(['get', coords]);\n\n\t\treturn p;\n\t},\n\n});\n\n\nL.vectorGrid.slicer = function (geojson, options) {\n\treturn new L.VectorGrid.Slicer(geojson, options);\n};\n\n", "\nimport Pbf from 'pbf';\nimport {VectorTile} from 'vector-tile';\n\n/*\n * 🍂class VectorGrid.Protobuf\n * 🍂extends VectorGrid\n *\n * A `VectorGrid` for vector tiles fetched from the internet.\n * Tiles are supposed to be protobufs (AKA \"protobuffer\" or \"Protocol Buffers\"),\n * containing data which complies with the\n * [MapBox Vector Tile Specification](https://github.com/mapbox/vector-tile-spec/tree/master/2.1).\n *\n * This is the format used by:\n * - Mapbox Vector Tiles\n * - Mapzen Vector Tiles\n * - ESRI Vector Tiles\n * - [OpenMapTiles hosted Vector Tiles](https://openmaptiles.com/hosting/)\n *\n * 🍂example\n *\n * You must initialize a `VectorGrid.Protobuf` with a URL template, just like in\n * `<PERSON>.TileLayer`s. The difference is that the template must point to vector tiles\n * (usually `.pbf` or `.mvt`) instead of raster (`.png` or `.jpg`) tiles, and that\n * you should define the styling for all the features.\n *\n * <br><br>\n *\n * For OpenMapTiles, with a key from [https://openmaptiles.org/docs/host/use-cdn/](https://openmaptiles.org/docs/host/use-cdn/),\n * initialization looks like this:\n *\n * ```\n * L.vectorGrid.protobuf(\"https://free-{s}.tilehosting.com/data/v3/{z}/{x}/{y}.pbf.pict?key={key}\", {\n * \tvectorTileLayerStyles: { ... },\n * \tsubdomains: \"0123\",\n * \tkey: 'abcdefghi01234567890',\n * \tmaxNativeZoom: 14\n * }).addTo(map);\n * ```\n *\n * And for Mapbox vector tiles, it looks like this:\n *\n * ```\n * L.vectorGrid.protobuf(\"https://{s}.tiles.mapbox.com/v4/mapbox.mapbox-streets-v6/{z}/{x}/{y}.vector.pbf?access_token={token}\", {\n * \tvectorTileLayerStyles: { ... },\n * \tsubdomains: \"abcd\",\n * \ttoken: \"pk.abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRTS.TUVWXTZ0123456789abcde\"\n * }).addTo(map);\n * ```\n */\nL.VectorGrid.Protobuf = L.VectorGrid.extend({\n\n\toptions: {\n\t\t// 🍂section\n\t\t// As with `L.TileLayer`, the URL template might contain a reference to\n\t\t// any option (see the example above and note the `{key}` or `token` in the URL\n\t\t// template, and the corresponding option).\n\t\t//\n\t\t// 🍂option subdomains: String = 'abc'\n\t\t// Akin to the `subdomains` option for `L.TileLayer`.\n\t\tsubdomains: 'abc',\t// Like L.TileLayer\n\t\t//\n\t\t// 🍂option fetchOptions: Object = {}\n\t\t// options passed to `fetch`, e.g. {credentials: 'same-origin'} to send cookie for the current domain\n\t\tfetchOptions: {}\n\t},\n\n\tinitialize: function(url, options) {\n\t\t// Inherits options from geojson-vt!\n// \t\tthis._slicer = geojsonvt(geojson, options);\n\t\tthis._url = url;\n\t\tL.VectorGrid.prototype.initialize.call(this, options);\n\t},\n\n\t// 🍂method setUrl(url: String, noRedraw?: Boolean): this\n\t// Updates the layer's URL template and redraws it (unless `noRedraw` is set to `true`).\n\tsetUrl: function(url, noRedraw) {\n\t\tthis._url = url;\n\n\t\tif (!noRedraw) {\n\t\t\tthis.redraw();\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t_getSubdomain: L.TileLayer.prototype._getSubdomain,\n\n\t_getVectorTilePromise: function(coords) {\n\t\tvar data = {\n\t\t\ts: this._getSubdomain(coords),\n\t\t\tx: coords.x,\n\t\t\ty: coords.y,\n\t\t\tz: coords.z\n// \t\t\tz: this._getZoomForUrl()\t/// TODO: Maybe replicate TileLayer's maxNativeZoom\n\t\t};\n\t\tif (this._map && !this._map.options.crs.infinite) {\n\t\t\tvar invertedY = this._globalTileRange.max.y - coords.y;\n\t\t\tif (this.options.tms) { // Should this option be available in Leaflet.VectorGrid?\n\t\t\t\tdata['y'] = invertedY;\n\t\t\t}\n\t\t\tdata['-y'] = invertedY;\n\t\t}\n\n\t\tvar tileUrl = L.Util.template(this._url, L.extend(data, this.options));\n\n\t\treturn fetch(tileUrl, this.options.fetchOptions).then(function(response){\n\n\t\t\tif (!response.ok) {\n\t\t\t\treturn {layers:[]};\n\t\t\t}\n\n\t\t\treturn response.blob().then( function (blob) {\n// \t\t\t\tconsole.log(blob);\n\n\t\t\t\tvar reader = new FileReader();\n\t\t\t\treturn new Promise(function(resolve){\n\t\t\t\t\treader.addEventListener(\"loadend\", function() {\n\t\t\t\t\t\t// reader.result contains the contents of blob as a typed array\n\n\t\t\t\t\t\t// blob.type === 'application/x-protobuf'\n\t\t\t\t\t\tvar pbf = new Pbf( reader.result );\n// \t\t\t\t\t\tconsole.log(pbf);\n\t\t\t\t\t\treturn resolve(new VectorTile( pbf ));\n\n\t\t\t\t\t});\n\t\t\t\t\treader.readAsArrayBuffer(blob);\n\t\t\t\t});\n\t\t\t});\n\t\t}).then(function(json){\n\n// \t\t\tconsole.log('Vector tile:', json.layers);\n// \t\t\tconsole.log('Vector tile water:', json.layers.water);\t// Instance of VectorTileLayer\n\n\t\t\t// Normalize feature getters into actual instanced features\n\t\t\tfor (var layerName in json.layers) {\n\t\t\t\tvar feats = [];\n\n\t\t\t\tfor (var i=0; i<json.layers[layerName].length; i++) {\n\t\t\t\t\tvar feat = json.layers[layerName].feature(i);\n\t\t\t\t\tfeat.geometry = feat.loadGeometry();\n\t\t\t\t\tfeats.push(feat);\n\t\t\t\t}\n\n\t\t\t\tjson.layers[layerName].features = feats;\n\t\t\t}\n\n\t\t\treturn json;\n\t\t});\n\t}\n});\n\n\n// 🍂factory L.vectorGrid.protobuf(url: String, options)\n// Instantiates a new protobuf VectorGrid with the given URL template and options\nL.vectorGrid.protobuf = function (url, options) {\n\treturn new L.VectorGrid.Protobuf(url, options);\n};\n\n", "module.exports.VectorTile = require('./lib/vectortile.js');\nmodule.exports.VectorTileFeature = require('./lib/vectortilefeature.js');\nmodule.exports.VectorTileLayer = require('./lib/vectortilelayer.js');\n", "'use strict';\n\nvar VectorTileLayer = require('./vectortilelayer');\n\nmodule.exports = VectorTile;\n\nfunction VectorTile(pbf, end) {\n    this.layers = pbf.readFields(readTile, {}, end);\n}\n\nfunction readTile(tag, layers, pbf) {\n    if (tag === 3) {\n        var layer = new VectorTileLayer(pbf, pbf.readVarint() + pbf.pos);\n        if (layer.length) layers[layer.name] = layer;\n    }\n}\n\n", "'use strict';\n\nvar VectorTileFeature = require('./vectortilefeature.js');\n\nmodule.exports = VectorTileLayer;\n\nfunction VectorTileLayer(pbf, end) {\n    // Public\n    this.version = 1;\n    this.name = null;\n    this.extent = 4096;\n    this.length = 0;\n\n    // Private\n    this._pbf = pbf;\n    this._keys = [];\n    this._values = [];\n    this._features = [];\n\n    pbf.readFields(readLayer, this, end);\n\n    this.length = this._features.length;\n}\n\nfunction readLayer(tag, layer, pbf) {\n    if (tag === 15) layer.version = pbf.readVarint();\n    else if (tag === 1) layer.name = pbf.readString();\n    else if (tag === 5) layer.extent = pbf.readVarint();\n    else if (tag === 2) layer._features.push(pbf.pos);\n    else if (tag === 3) layer._keys.push(pbf.readString());\n    else if (tag === 4) layer._values.push(readValueMessage(pbf));\n}\n\nfunction readValueMessage(pbf) {\n    var value = null,\n        end = pbf.readVarint() + pbf.pos;\n\n    while (pbf.pos < end) {\n        var tag = pbf.readVarint() >> 3;\n\n        value = tag === 1 ? pbf.readString() :\n            tag === 2 ? pbf.readFloat() :\n            tag === 3 ? pbf.readDouble() :\n            tag === 4 ? pbf.readVarint64() :\n            tag === 5 ? pbf.readVarint() :\n            tag === 6 ? pbf.readSVarint() :\n            tag === 7 ? pbf.readBoolean() : null;\n    }\n\n    return value;\n}\n\n// return feature `i` from this layer as a `VectorTileFeature`\nVectorTileLayer.prototype.feature = function(i) {\n    if (i < 0 || i >= this._features.length) throw new Error('feature index out of bounds');\n\n    this._pbf.pos = this._features[i];\n\n    var end = this._pbf.readVarint() + this._pbf.pos;\n    return new VectorTileFeature(this._pbf, end, this.extent, this._keys, this._values);\n};\n", "'use strict';\n\nvar Point = require('point-geometry');\n\nmodule.exports = VectorTileFeature;\n\nfunction VectorTileFeature(pbf, end, extent, keys, values) {\n    // Public\n    this.properties = {};\n    this.extent = extent;\n    this.type = 0;\n\n    // Private\n    this._pbf = pbf;\n    this._geometry = -1;\n    this._keys = keys;\n    this._values = values;\n\n    pbf.readFields(readFeature, this, end);\n}\n\nfunction readFeature(tag, feature, pbf) {\n    if (tag == 1) feature.id = pbf.readVarint();\n    else if (tag == 2) readTag(pbf, feature);\n    else if (tag == 3) feature.type = pbf.readVarint();\n    else if (tag == 4) feature._geometry = pbf.pos;\n}\n\nfunction readTag(pbf, feature) {\n    var end = pbf.readVarint() + pbf.pos;\n\n    while (pbf.pos < end) {\n        var key = feature._keys[pbf.readVarint()],\n            value = feature._values[pbf.readVarint()];\n        feature.properties[key] = value;\n    }\n}\n\nVectorTileFeature.types = ['Unknown', 'Point', 'LineString', 'Polygon'];\n\nVectorTileFeature.prototype.loadGeometry = function() {\n    var pbf = this._pbf;\n    pbf.pos = this._geometry;\n\n    var end = pbf.readVarint() + pbf.pos,\n        cmd = 1,\n        length = 0,\n        x = 0,\n        y = 0,\n        lines = [],\n        line;\n\n    while (pbf.pos < end) {\n        if (!length) {\n            var cmdLen = pbf.readVarint();\n            cmd = cmdLen & 0x7;\n            length = cmdLen >> 3;\n        }\n\n        length--;\n\n        if (cmd === 1 || cmd === 2) {\n            x += pbf.readSVarint();\n            y += pbf.readSVarint();\n\n            if (cmd === 1) { // moveTo\n                if (line) lines.push(line);\n                line = [];\n            }\n\n            line.push(new Point(x, y));\n\n        } else if (cmd === 7) {\n\n            // Workaround for https://github.com/mapbox/mapnik-vector-tile/issues/90\n            if (line) {\n                line.push(line[0].clone()); // closePolygon\n            }\n\n        } else {\n            throw new Error('unknown command ' + cmd);\n        }\n    }\n\n    if (line) lines.push(line);\n\n    return lines;\n};\n\nVectorTileFeature.prototype.bbox = function() {\n    var pbf = this._pbf;\n    pbf.pos = this._geometry;\n\n    var end = pbf.readVarint() + pbf.pos,\n        cmd = 1,\n        length = 0,\n        x = 0,\n        y = 0,\n        x1 = Infinity,\n        x2 = -Infinity,\n        y1 = Infinity,\n        y2 = -Infinity;\n\n    while (pbf.pos < end) {\n        if (!length) {\n            var cmdLen = pbf.readVarint();\n            cmd = cmdLen & 0x7;\n            length = cmdLen >> 3;\n        }\n\n        length--;\n\n        if (cmd === 1 || cmd === 2) {\n            x += pbf.readSVarint();\n            y += pbf.readSVarint();\n            if (x < x1) x1 = x;\n            if (x > x2) x2 = x;\n            if (y < y1) y1 = y;\n            if (y > y2) y2 = y;\n\n        } else if (cmd !== 7) {\n            throw new Error('unknown command ' + cmd);\n        }\n    }\n\n    return [x1, y1, x2, y2];\n};\n\nVectorTileFeature.prototype.toGeoJSON = function(x, y, z) {\n    var size = this.extent * Math.pow(2, z),\n        x0 = this.extent * x,\n        y0 = this.extent * y,\n        coords = this.loadGeometry(),\n        type = VectorTileFeature.types[this.type],\n        i, j;\n\n    function project(line) {\n        for (var j = 0; j < line.length; j++) {\n            var p = line[j], y2 = 180 - (p.y + y0) * 360 / size;\n            line[j] = [\n                (p.x + x0) * 360 / size - 180,\n                360 / Math.PI * Math.atan(Math.exp(y2 * Math.PI / 180)) - 90\n            ];\n        }\n    }\n\n    switch (this.type) {\n    case 1:\n        var points = [];\n        for (i = 0; i < coords.length; i++) {\n            points[i] = coords[i][0];\n        }\n        coords = points;\n        project(coords);\n        break;\n\n    case 2:\n        for (i = 0; i < coords.length; i++) {\n            project(coords[i]);\n        }\n        break;\n\n    case 3:\n        coords = classifyRings(coords);\n        for (i = 0; i < coords.length; i++) {\n            for (j = 0; j < coords[i].length; j++) {\n                project(coords[i][j]);\n            }\n        }\n        break;\n    }\n\n    if (coords.length === 1) {\n        coords = coords[0];\n    } else {\n        type = 'Multi' + type;\n    }\n\n    var result = {\n        type: \"Feature\",\n        geometry: {\n            type: type,\n            coordinates: coords\n        },\n        properties: this.properties\n    };\n\n    if ('id' in this) {\n        result.id = this.id;\n    }\n\n    return result;\n};\n\n// classifies an array of rings into polygons with outer rings and holes\n\nfunction classifyRings(rings) {\n    var len = rings.length;\n\n    if (len <= 1) return [rings];\n\n    var polygons = [],\n        polygon,\n        ccw;\n\n    for (var i = 0; i < len; i++) {\n        var area = signedArea(rings[i]);\n        if (area === 0) continue;\n\n        if (ccw === undefined) ccw = area < 0;\n\n        if (ccw === area < 0) {\n            if (polygon) polygons.push(polygon);\n            polygon = [rings[i]];\n\n        } else {\n            polygon.push(rings[i]);\n        }\n    }\n    if (polygon) polygons.push(polygon);\n\n    return polygons;\n}\n\nfunction signedArea(ring) {\n    var sum = 0;\n    for (var i = 0, len = ring.length, j = len - 1, p1, p2; i < len; j = i++) {\n        p1 = ring[i];\n        p2 = ring[j];\n        sum += (p2.x - p1.x) * (p1.y + p2.y);\n    }\n    return sum;\n}\n", "'use strict';\n\nmodule.exports = Point;\n\nfunction Point(x, y) {\n    this.x = x;\n    this.y = y;\n}\n\nPoint.prototype = {\n    clone: function() { return new Point(this.x, this.y); },\n\n    add:     function(p) { return this.clone()._add(p);     },\n    sub:     function(p) { return this.clone()._sub(p);     },\n    mult:    function(k) { return this.clone()._mult(k);    },\n    div:     function(k) { return this.clone()._div(k);     },\n    rotate:  function(a) { return this.clone()._rotate(a);  },\n    matMult: function(m) { return this.clone()._matMult(m); },\n    unit:    function() { return this.clone()._unit(); },\n    perp:    function() { return this.clone()._perp(); },\n    round:   function() { return this.clone()._round(); },\n\n    mag: function() {\n        return Math.sqrt(this.x * this.x + this.y * this.y);\n    },\n\n    equals: function(p) {\n        return this.x === p.x &&\n               this.y === p.y;\n    },\n\n    dist: function(p) {\n        return Math.sqrt(this.distSqr(p));\n    },\n\n    distSqr: function(p) {\n        var dx = p.x - this.x,\n            dy = p.y - this.y;\n        return dx * dx + dy * dy;\n    },\n\n    angle: function() {\n        return Math.atan2(this.y, this.x);\n    },\n\n    angleTo: function(b) {\n        return Math.atan2(this.y - b.y, this.x - b.x);\n    },\n\n    angleWith: function(b) {\n        return this.angleWithSep(b.x, b.y);\n    },\n\n    // Find the angle of the two vectors, solving the formula for the cross product a x b = |a||b|sin(θ) for θ.\n    angleWithSep: function(x, y) {\n        return Math.atan2(\n            this.x * y - this.y * x,\n            this.x * x + this.y * y);\n    },\n\n    _matMult: function(m) {\n        var x = m[0] * this.x + m[1] * this.y,\n            y = m[2] * this.x + m[3] * this.y;\n        this.x = x;\n        this.y = y;\n        return this;\n    },\n\n    _add: function(p) {\n        this.x += p.x;\n        this.y += p.y;\n        return this;\n    },\n\n    _sub: function(p) {\n        this.x -= p.x;\n        this.y -= p.y;\n        return this;\n    },\n\n    _mult: function(k) {\n        this.x *= k;\n        this.y *= k;\n        return this;\n    },\n\n    _div: function(k) {\n        this.x /= k;\n        this.y /= k;\n        return this;\n    },\n\n    _unit: function() {\n        this._div(this.mag());\n        return this;\n    },\n\n    _perp: function() {\n        var y = this.y;\n        this.y = this.x;\n        this.x = -y;\n        return this;\n    },\n\n    _rotate: function(angle) {\n        var cos = Math.cos(angle),\n            sin = Math.sin(angle),\n            x = cos * this.x - sin * this.y,\n            y = sin * this.x + cos * this.y;\n        this.x = x;\n        this.y = y;\n        return this;\n    },\n\n    _round: function() {\n        this.x = Math.round(this.x);\n        this.y = Math.round(this.y);\n        return this;\n    }\n};\n\n// constructs Point from an array if necessary\nPoint.convert = function (a) {\n    if (a instanceof Point) {\n        return a;\n    }\n    if (Array.isArray(a)) {\n        return new Point(a[0], a[1]);\n    }\n    return a;\n};\n", "'use strict';\n\nmodule.exports = Pbf;\n\nvar ieee754 = require('ieee754');\n\nfunction Pbf(buf) {\n    this.buf = ArrayBuffer.isView && ArrayBuffer.isView(buf) ? buf : new Uint8Array(buf || 0);\n    this.pos = 0;\n    this.type = 0;\n    this.length = this.buf.length;\n}\n\nPbf.Varint  = 0; // varint: int32, int64, uint32, uint64, sint32, sint64, bool, enum\nPbf.Fixed64 = 1; // 64-bit: double, fixed64, sfixed64\nPbf.Bytes   = 2; // length-delimited: string, bytes, embedded messages, packed repeated fields\nPbf.Fixed32 = 5; // 32-bit: float, fixed32, sfixed32\n\nvar SHIFT_LEFT_32 = (1 << 16) * (1 << 16),\n    SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;\n\nPbf.prototype = {\n\n    destroy: function() {\n        this.buf = null;\n    },\n\n    // === READING =================================================================\n\n    readFields: function(readField, result, end) {\n        end = end || this.length;\n\n        while (this.pos < end) {\n            var val = this.readVarint(),\n                tag = val >> 3,\n                startPos = this.pos;\n\n            this.type = val & 0x7;\n            readField(tag, result, this);\n\n            if (this.pos === startPos) this.skip(val);\n        }\n        return result;\n    },\n\n    readMessage: function(readField, result) {\n        return this.readFields(readField, result, this.readVarint() + this.pos);\n    },\n\n    readFixed32: function() {\n        var val = readUInt32(this.buf, this.pos);\n        this.pos += 4;\n        return val;\n    },\n\n    readSFixed32: function() {\n        var val = readInt32(this.buf, this.pos);\n        this.pos += 4;\n        return val;\n    },\n\n    // 64-bit int handling is based on github.com/dpw/node-buffer-more-ints (MIT-licensed)\n\n    readFixed64: function() {\n        var val = readUInt32(this.buf, this.pos) + readUInt32(this.buf, this.pos + 4) * SHIFT_LEFT_32;\n        this.pos += 8;\n        return val;\n    },\n\n    readSFixed64: function() {\n        var val = readUInt32(this.buf, this.pos) + readInt32(this.buf, this.pos + 4) * SHIFT_LEFT_32;\n        this.pos += 8;\n        return val;\n    },\n\n    readFloat: function() {\n        var val = ieee754.read(this.buf, this.pos, true, 23, 4);\n        this.pos += 4;\n        return val;\n    },\n\n    readDouble: function() {\n        var val = ieee754.read(this.buf, this.pos, true, 52, 8);\n        this.pos += 8;\n        return val;\n    },\n\n    readVarint: function(isSigned) {\n        var buf = this.buf,\n            val, b;\n\n        b = buf[this.pos++]; val  =  b & 0x7f;        if (b < 0x80) return val;\n        b = buf[this.pos++]; val |= (b & 0x7f) << 7;  if (b < 0x80) return val;\n        b = buf[this.pos++]; val |= (b & 0x7f) << 14; if (b < 0x80) return val;\n        b = buf[this.pos++]; val |= (b & 0x7f) << 21; if (b < 0x80) return val;\n        b = buf[this.pos];   val |= (b & 0x0f) << 28;\n\n        return readVarintRemainder(val, isSigned, this);\n    },\n\n    readVarint64: function() { // for compatibility with v2.0.1\n        return this.readVarint(true);\n    },\n\n    readSVarint: function() {\n        var num = this.readVarint();\n        return num % 2 === 1 ? (num + 1) / -2 : num / 2; // zigzag encoding\n    },\n\n    readBoolean: function() {\n        return Boolean(this.readVarint());\n    },\n\n    readString: function() {\n        var end = this.readVarint() + this.pos,\n            str = readUtf8(this.buf, this.pos, end);\n        this.pos = end;\n        return str;\n    },\n\n    readBytes: function() {\n        var end = this.readVarint() + this.pos,\n            buffer = this.buf.subarray(this.pos, end);\n        this.pos = end;\n        return buffer;\n    },\n\n    // verbose for performance reasons; doesn't affect gzipped size\n\n    readPackedVarint: function(arr, isSigned) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readVarint(isSigned));\n        return arr;\n    },\n    readPackedSVarint: function(arr) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readSVarint());\n        return arr;\n    },\n    readPackedBoolean: function(arr) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readBoolean());\n        return arr;\n    },\n    readPackedFloat: function(arr) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readFloat());\n        return arr;\n    },\n    readPackedDouble: function(arr) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readDouble());\n        return arr;\n    },\n    readPackedFixed32: function(arr) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readFixed32());\n        return arr;\n    },\n    readPackedSFixed32: function(arr) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readSFixed32());\n        return arr;\n    },\n    readPackedFixed64: function(arr) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readFixed64());\n        return arr;\n    },\n    readPackedSFixed64: function(arr) {\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readSFixed64());\n        return arr;\n    },\n\n    skip: function(val) {\n        var type = val & 0x7;\n        if (type === Pbf.Varint) while (this.buf[this.pos++] > 0x7f) {}\n        else if (type === Pbf.Bytes) this.pos = this.readVarint() + this.pos;\n        else if (type === Pbf.Fixed32) this.pos += 4;\n        else if (type === Pbf.Fixed64) this.pos += 8;\n        else throw new Error('Unimplemented type: ' + type);\n    },\n\n    // === WRITING =================================================================\n\n    writeTag: function(tag, type) {\n        this.writeVarint((tag << 3) | type);\n    },\n\n    realloc: function(min) {\n        var length = this.length || 16;\n\n        while (length < this.pos + min) length *= 2;\n\n        if (length !== this.length) {\n            var buf = new Uint8Array(length);\n            buf.set(this.buf);\n            this.buf = buf;\n            this.length = length;\n        }\n    },\n\n    finish: function() {\n        this.length = this.pos;\n        this.pos = 0;\n        return this.buf.subarray(0, this.length);\n    },\n\n    writeFixed32: function(val) {\n        this.realloc(4);\n        writeInt32(this.buf, val, this.pos);\n        this.pos += 4;\n    },\n\n    writeSFixed32: function(val) {\n        this.realloc(4);\n        writeInt32(this.buf, val, this.pos);\n        this.pos += 4;\n    },\n\n    writeFixed64: function(val) {\n        this.realloc(8);\n        writeInt32(this.buf, val & -1, this.pos);\n        writeInt32(this.buf, Math.floor(val * SHIFT_RIGHT_32), this.pos + 4);\n        this.pos += 8;\n    },\n\n    writeSFixed64: function(val) {\n        this.realloc(8);\n        writeInt32(this.buf, val & -1, this.pos);\n        writeInt32(this.buf, Math.floor(val * SHIFT_RIGHT_32), this.pos + 4);\n        this.pos += 8;\n    },\n\n    writeVarint: function(val) {\n        val = +val || 0;\n\n        if (val > 0xfffffff || val < 0) {\n            writeBigVarint(val, this);\n            return;\n        }\n\n        this.realloc(4);\n\n        this.buf[this.pos++] =           val & 0x7f  | (val > 0x7f ? 0x80 : 0); if (val <= 0x7f) return;\n        this.buf[this.pos++] = ((val >>>= 7) & 0x7f) | (val > 0x7f ? 0x80 : 0); if (val <= 0x7f) return;\n        this.buf[this.pos++] = ((val >>>= 7) & 0x7f) | (val > 0x7f ? 0x80 : 0); if (val <= 0x7f) return;\n        this.buf[this.pos++] =   (val >>> 7) & 0x7f;\n    },\n\n    writeSVarint: function(val) {\n        this.writeVarint(val < 0 ? -val * 2 - 1 : val * 2);\n    },\n\n    writeBoolean: function(val) {\n        this.writeVarint(Boolean(val));\n    },\n\n    writeString: function(str) {\n        str = String(str);\n        this.realloc(str.length * 4);\n\n        this.pos++; // reserve 1 byte for short string length\n\n        var startPos = this.pos;\n        // write the string directly to the buffer and see how much was written\n        this.pos = writeUtf8(this.buf, str, this.pos);\n        var len = this.pos - startPos;\n\n        if (len >= 0x80) makeRoomForExtraLength(startPos, len, this);\n\n        // finally, write the message length in the reserved place and restore the position\n        this.pos = startPos - 1;\n        this.writeVarint(len);\n        this.pos += len;\n    },\n\n    writeFloat: function(val) {\n        this.realloc(4);\n        ieee754.write(this.buf, val, this.pos, true, 23, 4);\n        this.pos += 4;\n    },\n\n    writeDouble: function(val) {\n        this.realloc(8);\n        ieee754.write(this.buf, val, this.pos, true, 52, 8);\n        this.pos += 8;\n    },\n\n    writeBytes: function(buffer) {\n        var len = buffer.length;\n        this.writeVarint(len);\n        this.realloc(len);\n        for (var i = 0; i < len; i++) this.buf[this.pos++] = buffer[i];\n    },\n\n    writeRawMessage: function(fn, obj) {\n        this.pos++; // reserve 1 byte for short message length\n\n        // write the message directly to the buffer and see how much was written\n        var startPos = this.pos;\n        fn(obj, this);\n        var len = this.pos - startPos;\n\n        if (len >= 0x80) makeRoomForExtraLength(startPos, len, this);\n\n        // finally, write the message length in the reserved place and restore the position\n        this.pos = startPos - 1;\n        this.writeVarint(len);\n        this.pos += len;\n    },\n\n    writeMessage: function(tag, fn, obj) {\n        this.writeTag(tag, Pbf.Bytes);\n        this.writeRawMessage(fn, obj);\n    },\n\n    writePackedVarint:   function(tag, arr) { this.writeMessage(tag, writePackedVarint, arr);   },\n    writePackedSVarint:  function(tag, arr) { this.writeMessage(tag, writePackedSVarint, arr);  },\n    writePackedBoolean:  function(tag, arr) { this.writeMessage(tag, writePackedBoolean, arr);  },\n    writePackedFloat:    function(tag, arr) { this.writeMessage(tag, writePackedFloat, arr);    },\n    writePackedDouble:   function(tag, arr) { this.writeMessage(tag, writePackedDouble, arr);   },\n    writePackedFixed32:  function(tag, arr) { this.writeMessage(tag, writePackedFixed32, arr);  },\n    writePackedSFixed32: function(tag, arr) { this.writeMessage(tag, writePackedSFixed32, arr); },\n    writePackedFixed64:  function(tag, arr) { this.writeMessage(tag, writePackedFixed64, arr);  },\n    writePackedSFixed64: function(tag, arr) { this.writeMessage(tag, writePackedSFixed64, arr); },\n\n    writeBytesField: function(tag, buffer) {\n        this.writeTag(tag, Pbf.Bytes);\n        this.writeBytes(buffer);\n    },\n    writeFixed32Field: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed32);\n        this.writeFixed32(val);\n    },\n    writeSFixed32Field: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed32);\n        this.writeSFixed32(val);\n    },\n    writeFixed64Field: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed64);\n        this.writeFixed64(val);\n    },\n    writeSFixed64Field: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed64);\n        this.writeSFixed64(val);\n    },\n    writeVarintField: function(tag, val) {\n        this.writeTag(tag, Pbf.Varint);\n        this.writeVarint(val);\n    },\n    writeSVarintField: function(tag, val) {\n        this.writeTag(tag, Pbf.Varint);\n        this.writeSVarint(val);\n    },\n    writeStringField: function(tag, str) {\n        this.writeTag(tag, Pbf.Bytes);\n        this.writeString(str);\n    },\n    writeFloatField: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed32);\n        this.writeFloat(val);\n    },\n    writeDoubleField: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed64);\n        this.writeDouble(val);\n    },\n    writeBooleanField: function(tag, val) {\n        this.writeVarintField(tag, Boolean(val));\n    }\n};\n\nfunction readVarintRemainder(l, s, p) {\n    var buf = p.buf,\n        h, b;\n\n    b = buf[p.pos++]; h  = (b & 0x70) >> 4;  if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x7f) << 3;  if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x7f) << 10; if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x7f) << 17; if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x7f) << 24; if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x01) << 31; if (b < 0x80) return toNum(l, h, s);\n\n    throw new Error('Expected varint not more than 10 bytes');\n}\n\nfunction readPackedEnd(pbf) {\n    return pbf.type === Pbf.Bytes ?\n        pbf.readVarint() + pbf.pos : pbf.pos + 1;\n}\n\nfunction toNum(low, high, isSigned) {\n    if (isSigned) {\n        return high * 0x100000000 + (low >>> 0);\n    }\n\n    return ((high >>> 0) * 0x100000000) + (low >>> 0);\n}\n\nfunction writeBigVarint(val, pbf) {\n    var low, high;\n\n    if (val >= 0) {\n        low  = (val % 0x100000000) | 0;\n        high = (val / 0x100000000) | 0;\n    } else {\n        low  = ~(-val % 0x100000000);\n        high = ~(-val / 0x100000000);\n\n        if (low ^ 0xffffffff) {\n            low = (low + 1) | 0;\n        } else {\n            low = 0;\n            high = (high + 1) | 0;\n        }\n    }\n\n    if (val >= 0x10000000000000000 || val < -0x10000000000000000) {\n        throw new Error('Given varint doesn\\'t fit into 10 bytes');\n    }\n\n    pbf.realloc(10);\n\n    writeBigVarintLow(low, high, pbf);\n    writeBigVarintHigh(high, pbf);\n}\n\nfunction writeBigVarintLow(low, high, pbf) {\n    pbf.buf[pbf.pos++] = low & 0x7f | 0x80; low >>>= 7;\n    pbf.buf[pbf.pos++] = low & 0x7f | 0x80; low >>>= 7;\n    pbf.buf[pbf.pos++] = low & 0x7f | 0x80; low >>>= 7;\n    pbf.buf[pbf.pos++] = low & 0x7f | 0x80; low >>>= 7;\n    pbf.buf[pbf.pos]   = low & 0x7f;\n}\n\nfunction writeBigVarintHigh(high, pbf) {\n    var lsb = (high & 0x07) << 4;\n\n    pbf.buf[pbf.pos++] |= lsb         | ((high >>>= 3) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f | ((high >>>= 7) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f | ((high >>>= 7) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f | ((high >>>= 7) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f | ((high >>>= 7) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f;\n}\n\nfunction makeRoomForExtraLength(startPos, len, pbf) {\n    var extraLen =\n        len <= 0x3fff ? 1 :\n        len <= 0x1fffff ? 2 :\n        len <= 0xfffffff ? 3 : Math.ceil(Math.log(len) / (Math.LN2 * 7));\n\n    // if 1 byte isn't enough for encoding message length, shift the data to the right\n    pbf.realloc(extraLen);\n    for (var i = pbf.pos - 1; i >= startPos; i--) pbf.buf[i + extraLen] = pbf.buf[i];\n}\n\nfunction writePackedVarint(arr, pbf)   { for (var i = 0; i < arr.length; i++) pbf.writeVarint(arr[i]);   }\nfunction writePackedSVarint(arr, pbf)  { for (var i = 0; i < arr.length; i++) pbf.writeSVarint(arr[i]);  }\nfunction writePackedFloat(arr, pbf)    { for (var i = 0; i < arr.length; i++) pbf.writeFloat(arr[i]);    }\nfunction writePackedDouble(arr, pbf)   { for (var i = 0; i < arr.length; i++) pbf.writeDouble(arr[i]);   }\nfunction writePackedBoolean(arr, pbf)  { for (var i = 0; i < arr.length; i++) pbf.writeBoolean(arr[i]);  }\nfunction writePackedFixed32(arr, pbf)  { for (var i = 0; i < arr.length; i++) pbf.writeFixed32(arr[i]);  }\nfunction writePackedSFixed32(arr, pbf) { for (var i = 0; i < arr.length; i++) pbf.writeSFixed32(arr[i]); }\nfunction writePackedFixed64(arr, pbf)  { for (var i = 0; i < arr.length; i++) pbf.writeFixed64(arr[i]);  }\nfunction writePackedSFixed64(arr, pbf) { for (var i = 0; i < arr.length; i++) pbf.writeSFixed64(arr[i]); }\n\n// Buffer code below from https://github.com/feross/buffer, MIT-licensed\n\nfunction readUInt32(buf, pos) {\n    return ((buf[pos]) |\n        (buf[pos + 1] << 8) |\n        (buf[pos + 2] << 16)) +\n        (buf[pos + 3] * 0x1000000);\n}\n\nfunction writeInt32(buf, val, pos) {\n    buf[pos] = val;\n    buf[pos + 1] = (val >>> 8);\n    buf[pos + 2] = (val >>> 16);\n    buf[pos + 3] = (val >>> 24);\n}\n\nfunction readInt32(buf, pos) {\n    return ((buf[pos]) |\n        (buf[pos + 1] << 8) |\n        (buf[pos + 2] << 16)) +\n        (buf[pos + 3] << 24);\n}\n\nfunction readUtf8(buf, pos, end) {\n    var str = '';\n    var i = pos;\n\n    while (i < end) {\n        var b0 = buf[i];\n        var c = null; // codepoint\n        var bytesPerSequence =\n            b0 > 0xEF ? 4 :\n            b0 > 0xDF ? 3 :\n            b0 > 0xBF ? 2 : 1;\n\n        if (i + bytesPerSequence > end) break;\n\n        var b1, b2, b3;\n\n        if (bytesPerSequence === 1) {\n            if (b0 < 0x80) {\n                c = b0;\n            }\n        } else if (bytesPerSequence === 2) {\n            b1 = buf[i + 1];\n            if ((b1 & 0xC0) === 0x80) {\n                c = (b0 & 0x1F) << 0x6 | (b1 & 0x3F);\n                if (c <= 0x7F) {\n                    c = null;\n                }\n            }\n        } else if (bytesPerSequence === 3) {\n            b1 = buf[i + 1];\n            b2 = buf[i + 2];\n            if ((b1 & 0xC0) === 0x80 && (b2 & 0xC0) === 0x80) {\n                c = (b0 & 0xF) << 0xC | (b1 & 0x3F) << 0x6 | (b2 & 0x3F);\n                if (c <= 0x7FF || (c >= 0xD800 && c <= 0xDFFF)) {\n                    c = null;\n                }\n            }\n        } else if (bytesPerSequence === 4) {\n            b1 = buf[i + 1];\n            b2 = buf[i + 2];\n            b3 = buf[i + 3];\n            if ((b1 & 0xC0) === 0x80 && (b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80) {\n                c = (b0 & 0xF) << 0x12 | (b1 & 0x3F) << 0xC | (b2 & 0x3F) << 0x6 | (b3 & 0x3F);\n                if (c <= 0xFFFF || c >= 0x110000) {\n                    c = null;\n                }\n            }\n        }\n\n        if (c === null) {\n            c = 0xFFFD;\n            bytesPerSequence = 1;\n\n        } else if (c > 0xFFFF) {\n            c -= 0x10000;\n            str += String.fromCharCode(c >>> 10 & 0x3FF | 0xD800);\n            c = 0xDC00 | c & 0x3FF;\n        }\n\n        str += String.fromCharCode(c);\n        i += bytesPerSequence;\n    }\n\n    return str;\n}\n\nfunction writeUtf8(buf, str, pos) {\n    for (var i = 0, c, lead; i < str.length; i++) {\n        c = str.charCodeAt(i); // code point\n\n        if (c > 0xD7FF && c < 0xE000) {\n            if (lead) {\n                if (c < 0xDC00) {\n                    buf[pos++] = 0xEF;\n                    buf[pos++] = 0xBF;\n                    buf[pos++] = 0xBD;\n                    lead = c;\n                    continue;\n                } else {\n                    c = lead - 0xD800 << 10 | c - 0xDC00 | 0x10000;\n                    lead = null;\n                }\n            } else {\n                if (c > 0xDBFF || (i + 1 === str.length)) {\n                    buf[pos++] = 0xEF;\n                    buf[pos++] = 0xBF;\n                    buf[pos++] = 0xBD;\n                } else {\n                    lead = c;\n                }\n                continue;\n            }\n        } else if (lead) {\n            buf[pos++] = 0xEF;\n            buf[pos++] = 0xBF;\n            buf[pos++] = 0xBD;\n            lead = null;\n        }\n\n        if (c < 0x80) {\n            buf[pos++] = c;\n        } else {\n            if (c < 0x800) {\n                buf[pos++] = c >> 0x6 | 0xC0;\n            } else {\n                if (c < 0x10000) {\n                    buf[pos++] = c >> 0xC | 0xE0;\n                } else {\n                    buf[pos++] = c >> 0x12 | 0xF0;\n                    buf[pos++] = c >> 0xC & 0x3F | 0x80;\n                }\n                buf[pos++] = c >> 0x6 & 0x3F | 0x80;\n            }\n            buf[pos++] = c & 0x3F | 0x80;\n        }\n    }\n    return pos;\n}\n", "exports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = nBytes * 8 - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = nBytes * 8 - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "import {} from './Leaflet.Renderer.SVG.Tile.js';\nimport { PointSymbolizer } from './Symbolizer.Point.js';\nimport { LineSymbolizer } from './Symbolizer.Line.js';\nimport { FillSymbolizer } from './Symbolizer.Fill.js';\n\n/* 🍂class VectorGrid\n * 🍂inherits GridLayer\n *\n * A `VectorGrid` is a generic, abstract class for displaying tiled vector data.\n * it provides facilities for symbolizing and rendering the data in the vector\n * tiles, but lacks the functionality to fetch the vector tiles from wherever\n * they are.\n *\n * Extends Leaflet's `L.GridLayer`.\n */\n\nL.VectorGrid = L.GridLayer.extend({\n\n\toptions: {\n\t\t// 🍂option rendererFactory = L.svg.tile\n\t\t// A factory method which will be used to instantiate the per-tile renderers.\n\t\trendererFactory: L.svg.tile,\n\n\t\t// 🍂option vectorTileLayerStyles: Object = {}\n\t\t// A data structure holding initial symbolizer definitions for the vector features.\n\t\tvectorTileLayerStyles: {},\n\n\t\t// 🍂option interactive: Boolean = false\n\t\t// Whether this `VectorGrid` fires `Interactive Layer` events.\n\t\tinteractive: false,\n\n\t\t// 🍂option getFeatureId: Function = undefined\n\t\t// A function that, given a vector feature, returns an unique identifier for it, e.g.\n\t\t// `function(feat) { return feat.properties.uniqueIdField; }`.\n\t\t// Must be defined for `setFeatureStyle` to work.\n\t},\n\n\tinitialize: function(options) {\n\t\tL.setOptions(this, options);\n\t\tL.GridLayer.prototype.initialize.apply(this, arguments);\n\t\tif (this.options.getFeatureId) {\n\t\t\tthis._vectorTiles = {};\n\t\t\tthis._overriddenStyles = {};\n\t\t\tthis.on('tileunload', function(e) {\n\t\t\t\tvar key = this._tileCoordsToKey(e.coords),\n\t\t\t\t    tile = this._vectorTiles[key];\n\n\t\t\t\tif (tile && this._map) {\n\t\t\t\t\ttile.removeFrom(this._map);\n\t\t\t\t}\n\t\t\t\tdelete this._vectorTiles[key];\n\t\t\t}, this);\n\t\t}\n\t\tthis._dataLayerNames = {};\n\t},\n\n\tcreateTile: function(coords, done) {\n\t\tvar storeFeatures = this.options.getFeatureId;\n\n\t\tvar tileSize = this.getTileSize();\n\t\tvar renderer = this.options.rendererFactory(coords, tileSize, this.options);\n\n\t\tvar vectorTilePromise = this._getVectorTilePromise(coords);\n\n\t\tif (storeFeatures) {\n\t\t\tthis._vectorTiles[this._tileCoordsToKey(coords)] = renderer;\n\t\t\trenderer._features = {};\n\t\t}\n\n\t\tvectorTilePromise.then( function renderTile(vectorTile) {\n\t\t\tfor (var layerName in vectorTile.layers) {\n\t\t\t\tthis._dataLayerNames[layerName] = true;\n\t\t\t\tvar layer = vectorTile.layers[layerName];\n\n\t\t\t\tvar pxPerExtent = this.getTileSize().divideBy(layer.extent);\n\n\t\t\t\tvar layerStyle = this.options.vectorTileLayerStyles[ layerName ] ||\n\t\t\t\tL.Path.prototype.options;\n\n\t\t\t\tfor (var i = 0; i < layer.features.length; i++) {\n\t\t\t\t\tvar feat = layer.features[i];\n\t\t\t\t\tvar id;\n\n\t\t\t\t\tvar styleOptions = layerStyle;\n\t\t\t\t\tif (storeFeatures) {\n\t\t\t\t\t\tid = this.options.getFeatureId(feat);\n\t\t\t\t\t\tvar styleOverride = this._overriddenStyles[id];\n\t\t\t\t\t\tif (styleOverride) {\n\t\t\t\t\t\t\tif (styleOverride[layerName]) {\n\t\t\t\t\t\t\t\tstyleOptions = styleOverride[layerName];\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tstyleOptions = styleOverride;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif (styleOptions instanceof Function) {\n\t\t\t\t\t\tstyleOptions = styleOptions(feat.properties, coords.z);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!(styleOptions instanceof Array)) {\n\t\t\t\t\t\tstyleOptions = [styleOptions];\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!styleOptions.length) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar featureLayer = this._createLayer(feat, pxPerExtent);\n\n\t\t\t\t\tfor (var j = 0; j < styleOptions.length; j++) {\n\t\t\t\t\t\tvar style = L.extend({}, L.Path.prototype.options, styleOptions[j]);\n\t\t\t\t\t\tfeatureLayer.render(renderer, style);\n\t\t\t\t\t\trenderer._addPath(featureLayer);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (this.options.interactive) {\n\t\t\t\t\t\tfeatureLayer.makeInteractive();\n\t\t\t\t\t}\n\n\t\t\t\t\tif (storeFeatures) {\n\t\t\t\t\t\trenderer._features[id] = {\n\t\t\t\t\t\t\tlayerName: layerName,\n\t\t\t\t\t\t\tfeature: featureLayer\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}\n\t\t\tif (this._map != null) {\n\t\t\t\trenderer.addTo(this._map);\n\t\t\t}\n\t\t\tL.Util.requestAnimFrame(done.bind(coords, null, null));\n\t\t}.bind(this));\n\n\t\treturn renderer.getContainer();\n\t},\n\n\t// 🍂method setFeatureStyle(id: Number, layerStyle: L.Path Options): this\n\t// Given the unique ID for a vector features (as per the `getFeatureId` option),\n\t// re-symbolizes that feature across all tiles it appears in.\n\tsetFeatureStyle: function(id, layerStyle) {\n\t\tthis._overriddenStyles[id] = layerStyle;\n\n\t\tfor (var tileKey in this._vectorTiles) {\n\t\t\tvar tile = this._vectorTiles[tileKey];\n\t\t\tvar features = tile._features;\n\t\t\tvar data = features[id];\n\t\t\tif (data) {\n\t\t\t\tvar feat = data.feature;\n\n\t\t\t\tvar styleOptions = layerStyle;\n\t\t\t\tif (layerStyle[data.layerName]) {\n\t\t\t\t\tstyleOptions = layerStyle[data.layerName];\n\t\t\t\t}\n\n\t\t\t\tthis._updateStyles(feat, tile, styleOptions);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t},\n\n\t// 🍂method setFeatureStyle(id: Number): this\n\t// Reverts the effects of a previous `setFeatureStyle` call.\n\tresetFeatureStyle: function(id) {\n\t\tdelete this._overriddenStyles[id];\n\n\t\tfor (var tileKey in this._vectorTiles) {\n\t\t\tvar tile = this._vectorTiles[tileKey];\n\t\t\tvar features = tile._features;\n\t\t\tvar data = features[id];\n\t\t\tif (data) {\n\t\t\t\tvar feat = data.feature;\n\t\t\t\tvar styleOptions = this.options.vectorTileLayerStyles[ data.layerName ] ||\n\t\t\t\tL.Path.prototype.options;\n\t\t\t\tthis._updateStyles(feat, tile, styleOptions);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t},\n\n\t// 🍂method getDataLayerNames(): Array\n\t// Returns an array of strings, with all the known names of data layers in\n\t// the vector tiles displayed. Useful for introspection.\n\tgetDataLayerNames: function() {\n\t\treturn Object.keys(this._dataLayerNames);\n\t},\n\n\t_updateStyles: function(feat, renderer, styleOptions) {\n\t\tstyleOptions = (styleOptions instanceof Function) ?\n\t\t\tstyleOptions(feat.properties, renderer.getCoord().z) :\n\t\t\tstyleOptions;\n\n\t\tif (!(styleOptions instanceof Array)) {\n\t\t\tstyleOptions = [styleOptions];\n\t\t}\n\n\t\tfor (var j = 0; j < styleOptions.length; j++) {\n\t\t\tvar style = L.extend({}, L.Path.prototype.options, styleOptions[j]);\n\t\t\tfeat.updateStyle(renderer, style);\n\t\t}\n\t},\n\n\t_createLayer: function(feat, pxPerExtent, layerStyle) {\n\t\tvar layer;\n\t\tswitch (feat.type) {\n\t\tcase 1:\n\t\t\tlayer = new PointSymbolizer(feat, pxPerExtent);\n\t\t\tbreak;\n\t\tcase 2:\n\t\t\tlayer = new LineSymbolizer(feat, pxPerExtent);\n\t\t\tbreak;\n\t\tcase 3:\n\t\t\tlayer = new FillSymbolizer(feat, pxPerExtent);\n\t\t\tbreak;\n\t\t}\n\n\t\tif (this.options.interactive) {\n\t\t\tlayer.addEventParent(this);\n\t\t}\n\n\t\treturn layer;\n\t},\n});\n\n/*\n * 🍂section Extension methods\n *\n * Classes inheriting from `VectorGrid` **must** define the `_getVectorTilePromise` private method.\n *\n * 🍂method getVectorTilePromise(coords: Object): Promise\n * Given a `coords` object in the form of `{x: Number, y: Number, z: Number}`,\n * this function must return a `Promise` for a vector tile.\n *\n */\nL.vectorGrid = function (options) {\n\treturn new L.VectorGrid(options);\n};\n\n", "import { Symbolizer } from './Symbolizer.js'\nimport { PolyBase } from './Symbolizer.PolyBase.js'\n\n// 🍂class FillSymbolizer\n// 🍂inherits Polyline\n// A symbolizer for filled areas. Applies only to polygon features.\n\nexport var FillSymbolizer = L.Polygon.extend({\n\tincludes: [Symbolizer.prototype, PolyBase],\n\n\tinitialize: function(feature, pxPerExtent) {\n\t\tthis.properties = feature.properties;\n\t\tthis._makeFeatureParts(feature, pxPerExtent);\n\t},\n\n\trender: function(renderer, style) {\n\t\tSymbolizer.prototype.render.call(this, renderer, style);\n\t\tthis._updatePath();\n\t}\n});\n", "\nimport { Symbolizer } from './Symbolizer.js'\nimport { PolyBase } from './Symbolizer.PolyBase.js'\n\n// 🍂class LineSymbolizer\n// 🍂inherits Polyline\n// A symbolizer for lines. Can be applied to line and polygon features.\n\nexport var LineSymbolizer = L.Polyline.extend({\n\tincludes: [Symbolizer.prototype, PolyBase],\n\n\tinitialize: function(feature, pxPerExtent) {\n\t\tthis.properties = feature.properties;\n\t\tthis._makeFeatureParts(feature, pxPerExtent);\n\t},\n\n\trender: function(renderer, style) {\n\t\tstyle.fill = false;\n\t\tSymbolizer.prototype.render.call(this, renderer, style);\n\t\tthis._updatePath();\n\t},\n\n\tupdateStyle: function(renderer, style) {\n\t\tstyle.fill = false;\n\t\tSymbolizer.prototype.updateStyle.call(this, renderer, style);\n\t},\n});\n\n", "\nimport { Symbolizer } from './Symbolizer.js'\nimport { PolyBase } from './Symbolizer.PolyBase.js'\n\n// 🍂class PointSymbolizer\n// 🍂inherits CircleMarker\n// A symbolizer for points.\n\nexport var PointSymbolizer = L.CircleMarker.extend({\n\tincludes: Symbolizer.prototype,\n\n\tstatics: {\n\t\ticonCache: {}\n\t},\n\n\tinitialize: function(feature, pxPerExtent) {\n\t\tthis.properties = feature.properties;\n\t\tthis._makeFeatureParts(feature, pxPerExtent);\n\t},\n\n\trender: function(renderer, style) {\n\t\tSymbolizer.prototype.render.call(this, renderer, style);\n\t\tthis._radius = style.radius || L.CircleMarker.prototype.options.radius;\n\t\tthis._updatePath();\n\t},\n\n\t_makeFeatureParts: function(feat, pxPerExtent) {\n\t\tvar coord = feat.geometry[0];\n\t\tif (typeof coord[0] === 'object' && 'x' in coord[0]) {\n\t\t\t// Protobuf vector tiles return [{x: , y:}]\n\t\t\tthis._point = L.point(coord[0]).scaleBy(pxPerExtent);\n\t\t\tthis._empty = L.Util.falseFn;\n\t\t} else {\n\t\t\t// Geojson-vt returns [,]\n\t\t\tthis._point = L.point(coord).scaleBy(pxPerExtent);\n\t\t\tthis._empty = L.Util.falseFn;\n\t\t}\n\t},\n\n\tmakeInteractive: function() {\n\t\tthis._updateBounds();\n\t},\n\n\tupdateStyle: function(renderer, style) {\n\t\tthis._radius = style.radius || this._radius;\n\t\tthis._updateBounds();\n\t\treturn Symbolizer.prototype.updateStyle.call(this, renderer, style);\n\t},\n\n\t_updateBounds: function() {\n\t\tvar icon = this.options.icon\n\t\tif (icon) {\n\t\t\tvar size = L.point(icon.options.iconSize),\n\t\t\t    anchor = icon.options.iconAnchor ||\n\t\t\t             size && size.divideBy(2, true),\n\t\t\t    p = this._point.subtract(anchor);\n\t\t\tthis._pxBounds = new L.Bounds(p, p.add(icon.options.iconSize));\n\t\t} else {\n\t\t\tL.CircleMarker.prototype._updateBounds.call(this);\n\t\t}\n\t},\n\n\t_updatePath: function() {\n\t\tif (this.options.icon) {\n\t\t\tthis._renderer._updateIcon(this)\n\t\t} else {\n\t\t\tL.CircleMarker.prototype._updatePath.call(this);\n\t\t}\n\t},\n\n\t_getImage: function () {\n\t\tif (this.options.icon) {\n\t\t\tvar url = this.options.icon.options.iconUrl,\n\t\t\t    img = PointSymbolizer.iconCache[url];\n\t\t\tif (!img) {\n\t\t\t\tvar icon = this.options.icon;\n\t\t\t\timg = PointSymbolizer.iconCache[url] = icon.createIcon();\n\t\t\t}\n\t\t\treturn img;\n\t\t} else {\n\t\t\treturn null;\n\t\t}\n\n\t},\n\n\t_containsPoint: function(p) {\n\t\tvar icon = this.options.icon;\n\t\tif (icon) {\n\t\t\treturn this._pxBounds.contains(p);\n\t\t} else {\n\t\t\treturn L.CircleMarker.prototype._containsPoint.call(this, p);\n\t\t}\n\t}\n});\n\n", "\n// Contains mixins which are common to the Line Symbolizer and the Fill Symbolizer.\n\nexport var PolyBase = {\n\t_makeFeatureParts: function(feat, pxPerExtent) {\n\t\tvar rings = feat.geometry;\n\t\tvar coord;\n\n\t\tthis._parts = [];\n\t\tfor (var i = 0; i < rings.length; i++) {\n\t\t\tvar ring = rings[i];\n\t\t\tvar part = [];\n\t\t\tfor (var j = 0; j < ring.length; j++) {\n\t\t\t\tcoord = ring[j];\n\t\t\t\t// Protobuf vector tiles return {x: , y:}\n\t\t\t\t// Geojson-vt returns [,]\n\t\t\t\tpart.push(L.point(coord).scaleBy(pxPerExtent));\n\t\t\t}\n\t\t\tthis._parts.push(part);\n\t\t}\n\t},\n\n\tmakeInteractive: function() {\n\t\tthis._pxBounds = this._getPixelBounds();\n\t}\n};\n", "\n// 🍂class Symbolizer\n// 🍂inherits Class\n// The abstract Symbolizer class is mostly equivalent in concept to a `L.Path` - it's an interface for\n// polylines, polygons and circles. But instead of representing leaflet Layers,\n// it represents things that have to be drawn inside a vector tile.\n\n// A vector tile *data layer* might have zero, one, or more *symbolizer definitions*\n// A vector tile *feature* might have zero, one, or more *symbolizers*.\n// The actual symbolizers applied will depend on filters and the symbolizer functions.\n\nexport var Symbolizer = L.Class.extend({\n\t// 🍂method initialize(feature: GeoJSON, pxPerExtent: Number)\n\t// Initializes a new Line Symbolizer given a GeoJSON feature and the\n\t// pixel-to-coordinate-units ratio. Internal use only.\n\n\t// 🍂method render(renderer, style)\n\t// Renders this symbolizer in the given tiled renderer, with the given\n\t// `L.Path` options.  Internal use only.\n\trender: function(renderer, style) {\n\t\tthis._renderer = renderer;\n\t\tthis.options = style;\n\t\trenderer._initPath(this);\n\t\trenderer._updateStyle(this);\n\t},\n\n\t// 🍂method render(renderer, style)\n\t// Updates the `L.Path` options used to style this symbolizer, and re-renders it.\n\t// Internal use only.\n\tupdateStyle: function(renderer, style) {\n\t\tthis.options = style;\n\t\trenderer._updateStyle(this);\n\t},\n\n\t_getPixelBounds: function() {\n\t\tvar parts = this._parts;\n\t\tvar bounds = L.bounds([]);\n\t\tfor (var i = 0; i < parts.length; i++) {\n\t\t\tvar part = parts[i];\n\t\t\tfor (var j = 0; j < part.length; j++) {\n\t\t\t\tbounds.extend(part[j]);\n\t\t\t}\n\t\t}\n\n\t\tvar w = this._clickTolerance(),\n\t\t    p = new L.Point(w, w);\n\n\t\tbounds.min._subtract(p);\n\t\tbounds.max._add(p);\n\n\t\treturn bounds;\n\t},\n\t_clickTolerance: L.Path.prototype._clickTolerance,\n});\n\n", "\n\nL.SVG.Tile = L.SVG.extend({\n\n\tinitialize: function (tileCoord, tileSize, options) {\n\t\tL.SVG.prototype.initialize.call(this, options);\n\t\tthis._tileCoord = tileCoord;\n\t\tthis._size = tileSize;\n\n\t\tthis._initContainer();\n\t\tthis._container.setAttribute('width', this._size.x);\n\t\tthis._container.setAttribute('height', this._size.y);\n\t\tthis._container.setAttribute('viewBox', [0, 0, this._size.x, this._size.y].join(' '));\n\n\t\tthis._layers = {};\n\t},\n\n\tgetCoord: function() {\n\t\treturn this._tileCoord;\n\t},\n\n\tgetContainer: function() {\n\t\treturn this._container;\n\t},\n\n\tonAdd: L.Util.falseFn,\n\n\taddTo: function(map) {\n\t\tthis._map = map;\n\t\tif (this.options.interactive) {\n\t\t\tfor (var i in this._layers) {\n\t\t\t\tvar layer = this._layers[i];\n\t\t\t\t// By default, Leaflet tiles do not have pointer events.\n\t\t\t\tlayer._path.style.pointerEvents = 'auto';\n\t\t\t\tthis._map._targets[L.stamp(layer._path)] = layer;\n\t\t\t}\n\t\t}\n\t},\n\n\tremoveFrom: function (map) {\n\t\tif (this.options.interactive) {\n\t\t\tfor (var i in this._layers) {\n\t\t\t\tvar layer = this._layers[i];\n\t\t\t\tdelete this._map._targets[L.stamp(layer._path)];\n\t\t\t}\n\t\t}\n\t\tdelete this._map;\n\t},\n\n\t_initContainer: function() {\n\t\tL.SVG.prototype._initContainer.call(this);\n\t\tvar rect =  L.SVG.create('rect');\n\t},\n\n\t/// TODO: Modify _initPath to include an extra parameter, a group name\n\t/// to order symbolizers by z-index\n\n\t_addPath: function (layer) {\n\t\tthis._rootGroup.appendChild(layer._path);\n\t\tthis._layers[L.stamp(layer)] = layer;\n\t},\n\n\t_updateIcon: function (layer) {\n\t\tvar path = layer._path = L.SVG.create('image'),\n\t\t    icon = layer.options.icon,\n\t\t    options = icon.options,\n\t\t    size = L.point(options.iconSize),\n\t\t    anchor = options.iconAnchor ||\n\t\t        \t size && size.divideBy(2, true),\n\t\t    p = layer._point.subtract(anchor);\n\t\tpath.setAttribute('x', p.x);\n\t\tpath.setAttribute('y', p.y);\n\t\tpath.setAttribute('width', size.x + 'px');\n\t\tpath.setAttribute('height', size.y + 'px');\n\t\tpath.setAttribute('href', options.iconUrl);\n\t}\n});\n\n\nL.svg.tile = function(tileCoord, tileSize, opts){\n\treturn new L.SVG.Tile(tileCoord, tileSize, opts);\n}\n\n"], "names": ["Pbf", "require$$2", "VectorTileLayer", "VectorTile", "require$$0", "VectorTileFeature", "Point", "this"], "mappings": ";;;AiBEA,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;;CAEzB,UAAU,EAAE,UAAU,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE;EACnD,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;EAC/C,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;EAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;;EAEtB,IAAI,CAAC,cAAc,EAAE,CAAC;EACtB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACpD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACrD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;EAEtF,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;EAClB;;CAED,QAAQ,EAAE,WAAW;EACpB,OAAO,IAAI,CAAC,UAAU,CAAC;EACvB;;CAED,YAAY,EAAE,WAAW;EACxB,OAAO,IAAI,CAAC,UAAU,CAAC;EACvB;;CAED,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO;;CAErB,KAAK,EAAE,SAAS,GAAG,EAAE;EACpB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;EAChB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;GAC7B,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;IAC3B,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;IAE5B,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;IACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;IACjD;GACD;EACD;;CAED,UAAU,EAAE,UAAU,GAAG,EAAE;EAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;GAC7B,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;IAC3B,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD;GACD;EACD,OAAO,IAAI,CAAC,IAAI,CAAC;EACjB;;CAED,cAAc,EAAE,WAAW;EAC1B,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC1C,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;EACjC;;;;;CAKD,QAAQ,EAAE,UAAU,KAAK,EAAE;EAC1B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;EACzC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;EACrC;;CAED,WAAW,EAAE,UAAU,KAAK,EAAE;EAC7B,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC;MAC1C,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI;MACzB,OAAO,GAAG,IAAI,CAAC,OAAO;MACtB,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAM,GAAG,OAAO,CAAC,UAAU;YACrB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;MACpC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EACtC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;EAC1C,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;EAC3C,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;EAC3C;CACD,CAAC,CAAC;;;AAGH,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,SAAS,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;CAC/C,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;CACjD,CAAA;;ADhFD;;;;;;;;;;AAUA,AAAO,IAAI,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;;;;;;;;CAQtC,MAAM,EAAE,SAAS,QAAQ,EAAE,KAAK,EAAE;EACjC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;EAC1B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;EACrB,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;EACzB,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;EAC5B;;;;;CAKD,WAAW,EAAE,SAAS,QAAQ,EAAE,KAAK,EAAE;EACtC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;EACrB,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;EAC5B;;CAED,eAAe,EAAE,WAAW;EAC3B,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;EACxB,IAAI,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;EAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;GACtC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;GACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;GACD;;EAED,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE;MAC1B,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE1B,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EACxB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEnB,OAAO,MAAM,CAAC;EACd;CACD,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe;CACjD,CAAC,CAAC;;ADpDH;;AAEA,AAAO,IAAI,QAAQ,GAAG;CACrB,iBAAiB,EAAE,SAAS,IAAI,EAAE,WAAW,EAAE;EAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;EAC1B,IAAI,KAAK,CAAC;;EAEV,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;EACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;GACtC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;GACpB,IAAI,IAAI,GAAG,EAAE,CAAC;GACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACrC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;;;IAGhB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;IAC/C;GACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;GACvB;EACD;;CAED,eAAe,EAAE,WAAW;EAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;EACxC;CACD,CAAC;;ADrBF;;;;AAIA,AAAO,IAAI,eAAe,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;CAClD,QAAQ,EAAE,UAAU,CAAC,SAAS;;CAE9B,OAAO,EAAE;EACR,SAAS,EAAE,EAAE;EACb;;CAED,UAAU,EAAE,SAAS,OAAO,EAAE,WAAW,EAAE;EAC1C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;EACrC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;EAC7C;;CAED,MAAM,EAAE,SAAS,QAAQ,EAAE,KAAK,EAAE;EACjC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EACxD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;EACvE,IAAI,CAAC,WAAW,EAAE,CAAC;EACnB;;CAED,iBAAiB,EAAE,SAAS,IAAI,EAAE,WAAW,EAAE;EAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;;GAEpD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;GACrD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;GAC7B,MAAM;;GAEN,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;GAClD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;GAC7B;EACD;;CAED,eAAe,EAAE,WAAW;EAC3B,IAAI,CAAC,aAAa,EAAE,CAAC;EACrB;;CAED,WAAW,EAAE,SAAS,QAAQ,EAAE,KAAK,EAAE;EACtC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;EAC5C,IAAI,CAAC,aAAa,EAAE,CAAC;EACrB,OAAO,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EACpE;;CAED,aAAa,EAAE,WAAW;EACzB,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;EAC5B,IAAI,IAAI,EAAE;GACT,IAAI,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;OACrC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU;gBACvB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;OACvC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;GACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;GAC/D,MAAM;GACN,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;GAClD;EACD;;CAED,WAAW,EAAE,WAAW;EACvB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;GACtB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;GAChC,MAAM;GACN,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;GAChD;EACD;;CAED,SAAS,EAAE,YAAY;EACtB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;GACtB,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;OACvC,GAAG,GAAG,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;GACzC,IAAI,CAAC,GAAG,EAAE;IACT,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAC7B,GAAG,GAAG,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;IACzD;GACD,OAAO,GAAG,CAAC;GACX,MAAM;GACN,OAAO,IAAI,CAAC;GACZ;;EAED;;CAED,cAAc,EAAE,SAAS,CAAC,EAAE;EAC3B,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;EAC7B,IAAI,IAAI,EAAE;GACT,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;GAClC,MAAM;GACN,OAAO,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;GAC7D;EACD;CACD,CAAC,CAAC;;ADzFH;;;;AAIA,AAAO,IAAI,cAAc,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;CAC7C,QAAQ,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC;;CAE1C,UAAU,EAAE,SAAS,OAAO,EAAE,WAAW,EAAE;EAC1C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;EACrC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;EAC7C;;CAED,MAAM,EAAE,SAAS,QAAQ,EAAE,KAAK,EAAE;EACjC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;EACnB,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EACxD,IAAI,CAAC,WAAW,EAAE,CAAC;EACnB;;CAED,WAAW,EAAE,SAAS,QAAQ,EAAE,KAAK,EAAE;EACtC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;EACnB,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EAC7D;CACD,CAAC,CAAC;;ADvBH;;;;AAIA,AAAO,IAAI,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;CAC5C,QAAQ,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC;;CAE1C,UAAU,EAAE,SAAS,OAAO,EAAE,WAAW,EAAE;EAC1C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;EACrC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;EAC7C;;CAED,MAAM,EAAE,SAAS,QAAQ,EAAE,KAAK,EAAE;EACjC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EACxD,IAAI,CAAC,WAAW,EAAE,CAAC;EACnB;CACD,CAAC,CAAC;;ADdH;;;;;;;;;;;AAWA,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;;CAEjC,OAAO,EAAE;;;EAGR,eAAe,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI;;;;EAI3B,qBAAqB,EAAE,EAAE;;;;EAIzB,WAAW,EAAE,KAAK;;;;;;EAMlB;;CAED,UAAU,EAAE,SAAS,OAAO,EAAE;EAC7B,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;EAC5B,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EACxD,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;GAC9B,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;GACvB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;GAC5B,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,EAAE;IACjC,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC;QACrC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;;IAElC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;KACtB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC3B;IACD,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAC9B,EAAE,IAAI,CAAC,CAAC;GACT;EACD,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;EAC1B;;CAED,UAAU,EAAE,SAAS,MAAM,EAAE,IAAI,EAAE;EAClC,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;;EAE9C,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;EAClC,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;;EAE5E,IAAI,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;;EAE3D,IAAI,aAAa,EAAE;GAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC;GAC5D,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;GACxB;;EAED,iBAAiB,CAAC,IAAI,EAAE,SAAS,UAAU,CAAC,UAAU,EAAE;GACvD,KAAK,IAAI,SAAS,IAAI,UAAU,CAAC,MAAM,EAAE;IACxC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;IACvC,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;;IAEzC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;;IAE5D,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,SAAS,EAAE;IAChE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;;IAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;KAC/C,IAAI,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC7B,IAAI,EAAE,CAAC;;KAEP,IAAI,YAAY,GAAG,UAAU,CAAC;KAC9B,IAAI,aAAa,EAAE;MAClB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;MACrC,IAAI,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;MAC/C,IAAI,aAAa,EAAE;OAClB,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE;QAC7B,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;QACxC,MAAM;QACN,YAAY,GAAG,aAAa,CAAC;QAC7B;OACD;MACD;;KAED,IAAI,YAAY,YAAY,QAAQ,EAAE;MACrC,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;MACvD;;KAED,IAAI,CAAC,CAAC,YAAY,YAAY,KAAK,CAAC,EAAE;MACrC,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;MAC9B;;KAED,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;MACzB,SAAS;MACT;;KAED,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;;KAExD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MAC7C,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;MACrC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;MAChC;;KAED,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;MAC7B,YAAY,CAAC,eAAe,EAAE,CAAC;MAC/B;;KAED,IAAI,aAAa,EAAE;MAClB,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG;OACxB,SAAS,EAAE,SAAS;OACpB,OAAO,EAAE,YAAY;OACrB,CAAC;MACF;KACD;;IAED;GACD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;IACtB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B;GACD,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;GACvD,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEd,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;EAC/B;;;;;CAKD,eAAe,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE;EACzC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;;EAExC,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;GACtC,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;GACtC,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;GAC9B,IAAI,IAAI,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;GACxB,IAAI,IAAI,EAAE;IACT,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;;IAExB,IAAI,YAAY,GAAG,UAAU,CAAC;IAC9B,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;KAC/B,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC1C;;IAED,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IAC7C;GACD;EACD,OAAO,IAAI,CAAC;EACZ;;;;CAID,iBAAiB,EAAE,SAAS,EAAE,EAAE;EAC/B,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;;EAElC,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;GACtC,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;GACtC,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;GAC9B,IAAI,IAAI,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;GACxB,IAAI,IAAI,EAAE;IACT,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;IACxB,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,EAAE;IACvE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IACzB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IAC7C;GACD;EACD,OAAO,IAAI,CAAC;EACZ;;;;;CAKD,iBAAiB,EAAE,WAAW;EAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EACzC;;CAED,aAAa,EAAE,SAAS,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE;EACrD,YAAY,GAAG,CAAC,YAAY,YAAY,QAAQ,CAAC;GAChD,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;GACpD,YAAY,CAAC;;EAEd,IAAI,CAAC,CAAC,YAAY,YAAY,KAAK,CAAC,EAAE;GACrC,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;GAC9B;;EAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;GAC7C,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;GACpE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;GAClC;EACD;;CAED,YAAY,EAAE,SAAS,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE;EACrD,IAAI,KAAK,CAAC;EACV,QAAQ,IAAI,CAAC,IAAI;EACjB,KAAK,CAAC;GACL,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;GAC/C,MAAM;EACP,KAAK,CAAC;GACL,KAAK,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;GAC9C,MAAM;EACP,KAAK,CAAC;GACL,KAAK,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;GAC9C,MAAM;GACN;;EAED,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;GAC7B,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;GAC3B;;EAED,OAAO,KAAK,CAAC;EACb;CACD,CAAC,CAAC;;;;;;;;;;;;AAYH,CAAC,CAAC,UAAU,GAAG,UAAU,OAAO,EAAE;CACjC,OAAO,IAAI,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;CACjC,CAAC;;AD7OF,WAAe,UAAU,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;EAC3D,IAAI,CAAC,EAAE,CAAC,CAAA;EACR,IAAI,IAAI,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA;EAChC,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAA;EAC1B,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,CAAA;EACrB,IAAI,KAAK,GAAG,CAAC,CAAC,CAAA;EACd,IAAI,CAAC,GAAG,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,CAAA;EAC/B,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;EACrB,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;;EAE1B,CAAC,IAAI,CAAC,CAAA;;EAEN,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;EAC7B,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;EACd,KAAK,IAAI,IAAI,CAAA;EACb,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;;EAE1E,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;EAC7B,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;EACd,KAAK,IAAI,IAAI,CAAA;EACb,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;;EAE1E,IAAI,CAAC,KAAK,CAAC,EAAE;IACX,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;GACd,MAAM,IAAI,CAAC,KAAK,IAAI,EAAE;IACrB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC;GAC3C,MAAM;IACL,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;IACzB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;GACd;EACD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;CAChD,CAAA;;AAED,YAAgB,UAAU,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;EACnE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;EACX,IAAI,IAAI,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA;EAChC,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAA;EAC1B,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,CAAA;EACrB,IAAI,EAAE,IAAI,IAAI,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;EAChE,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAA;EAC/B,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;EACrB,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;;EAE3D,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;;EAEvB,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,QAAQ,EAAE;IACtC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACxB,CAAC,GAAG,IAAI,CAAA;GACT,MAAM;IACL,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;IAC1C,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACrC,CAAC,EAAE,CAAA;MACH,CAAC,IAAI,CAAC,CAAA;KACP;IACD,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,EAAE;MAClB,KAAK,IAAI,EAAE,GAAG,CAAC,CAAA;KAChB,MAAM;MACL,KAAK,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAA;KACrC;IACD,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;MAClB,CAAC,EAAE,CAAA;MACH,CAAC,IAAI,CAAC,CAAA;KACP;;IAED,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,EAAE;MACrB,CAAC,GAAG,CAAC,CAAA;MACL,CAAC,GAAG,IAAI,CAAA;KACT,MAAM,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,EAAE;MACzB,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;MACvC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;KACd,MAAM;MACL,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;MACtD,CAAC,GAAG,CAAC,CAAA;KACN;GACF;;EAED,OAAO,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE;;EAEhF,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAA;EACnB,IAAI,IAAI,IAAI,CAAA;EACZ,OAAO,IAAI,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE;;EAE/E,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;CAClC,CAAA;;;;;;;ADjFD,SAAc,GAAG,GAAG,CAAC;;AAErB,IAAI,OAAO,GAAGI,OAAkB,CAAC;;AAEjC,SAAS,GAAG,CAAC,GAAG,EAAE;IACd,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAC1F,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACb,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;CACjC;;AAED,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;AAChB,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAChB,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;AAChB,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;;AAEhB,IAAI,aAAa,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;IACrC,cAAc,GAAG,CAAC,GAAG,aAAa,CAAC;;AAEvC,GAAG,CAAC,SAAS,GAAG;;IAEZ,OAAO,EAAE,WAAW;QAChB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;KACnB;;;;IAID,UAAU,EAAE,SAAS,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE;;;QACzC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC;;QAEzB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;YACnB,IAAI,GAAG,GAAGG,MAAI,CAAC,UAAU,EAAE;gBACvB,GAAG,GAAG,GAAG,IAAI,CAAC;gBACd,QAAQ,GAAGA,MAAI,CAAC,GAAG,CAAC;;YAExBA,MAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;YACtB,SAAS,CAAC,GAAG,EAAE,MAAM,EAAEA,MAAI,CAAC,CAAC;;YAE7B,IAAIA,MAAI,CAAC,GAAG,KAAK,QAAQ,EAAE,EAAAA,MAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAA;SAC7C;QACD,OAAO,MAAM,CAAC;KACjB;;IAED,WAAW,EAAE,SAAS,SAAS,EAAE,MAAM,EAAE;QACrC,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;KAC3E;;IAED,WAAW,EAAE,WAAW;QACpB,IAAI,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,GAAG,CAAC;KACd;;IAED,YAAY,EAAE,WAAW;QACrB,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,GAAG,CAAC;KACd;;;;IAID,WAAW,EAAE,WAAW;QACpB,IAAI,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;QAC9F,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,GAAG,CAAC;KACd;;IAED,YAAY,EAAE,WAAW;QACrB,IAAI,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;QAC7F,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,GAAG,CAAC;KACd;;IAED,SAAS,EAAE,WAAW;QAClB,IAAI,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,GAAG,CAAC;KACd;;IAED,UAAU,EAAE,WAAW;QACnB,IAAI,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,GAAG,CAAC;KACd;;IAED,UAAU,EAAE,SAAS,QAAQ,EAAE;QAC3B,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG;YACd,GAAG,EAAE,CAAC,CAAC;;QAEX,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,GAAG,CAAC,EAAA;QACvE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,GAAG,CAAC,EAAA;QACvE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,GAAG,CAAC,EAAA;QACvE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,GAAG,CAAC,EAAA;QACvE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC;;QAE7C,OAAO,mBAAmB,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;KACnD;;IAED,YAAY,EAAE,WAAW;QACrB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KAChC;;IAED,WAAW,EAAE,WAAW;QACpB,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC5B,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;KACnD;;IAED,WAAW,EAAE,WAAW;QACpB,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KACrC;;IAED,UAAU,EAAE,WAAW;QACnB,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,GAAG;YAClC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,OAAO,GAAG,CAAC;KACd;;IAED,SAAS,EAAE,WAAW;QAClB,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,GAAG;YAClC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,OAAO,MAAM,CAAC;KACjB;;;;IAID,gBAAgB,EAAE,SAAS,GAAG,EAAE,QAAQ,EAAE;;;QACtC,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAA;QAC3D,OAAO,GAAG,CAAC;KACd;IACD,iBAAiB,EAAE,SAAS,GAAG,EAAE;;;QAC7B,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAA;QACpD,OAAO,GAAG,CAAC;KACd;IACD,iBAAiB,EAAE,SAAS,GAAG,EAAE;;;QAC7B,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAA;QACpD,OAAO,GAAG,CAAC;KACd;IACD,eAAe,EAAE,SAAS,GAAG,EAAE;;;QAC3B,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAA;QAClD,OAAO,GAAG,CAAC;KACd;IACD,gBAAgB,EAAE,SAAS,GAAG,EAAE;;;QAC5B,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,UAAU,EAAE,CAAC,CAAC,EAAA;QACnD,OAAO,GAAG,CAAC;KACd;IACD,iBAAiB,EAAE,SAAS,GAAG,EAAE;;;QAC7B,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAA;QACpD,OAAO,GAAG,CAAC;KACd;IACD,kBAAkB,EAAE,SAAS,GAAG,EAAE;;;QAC9B,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,YAAY,EAAE,CAAC,CAAC,EAAA;QACrD,OAAO,GAAG,CAAC;KACd;IACD,iBAAiB,EAAE,SAAS,GAAG,EAAE;;;QAC7B,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAA;QACpD,OAAO,GAAG,CAAC;KACd;IACD,kBAAkB,EAAE,SAAS,GAAG,EAAE;;;QAC9B,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,GAAG,CAAC,IAAI,CAACA,MAAI,CAAC,YAAY,EAAE,CAAC,CAAC,EAAA;QACrD,OAAO,GAAG,CAAC;KACd;;IAED,IAAI,EAAE,SAAS,GAAG,EAAE;QAChB,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;QACrB,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,EAAE,EAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,EAAA;aAC1D,IAAI,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,EAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAA;aAChE,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,EAAA,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAA;aACxC,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,EAAA,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAA;aACxC,EAAA,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,EAAA;KACvD;;;;IAID,QAAQ,EAAE,SAAS,GAAG,EAAE,IAAI,EAAE;QAC1B,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;KACvC;;IAED,OAAO,EAAE,SAAS,GAAG,EAAE;QACnB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;;QAE/B,OAAO,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,MAAM,IAAI,CAAC,CAAC,EAAA;;QAE5C,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;YACxB,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;YACjC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACxB;KACJ;;IAED,MAAM,EAAE,WAAW;QACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;QACvB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KAC5C;;IAED,YAAY,EAAE,SAAS,GAAG,EAAE;QACxB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChB,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACjB;;IAED,aAAa,EAAE,SAAS,GAAG,EAAE;QACzB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChB,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACjB;;IAED,YAAY,EAAE,SAAS,GAAG,EAAE;QACxB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChB,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACzC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,cAAc,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACjB;;IAED,aAAa,EAAE,SAAS,GAAG,EAAE;QACzB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChB,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACzC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,cAAc,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACjB;;IAED,WAAW,EAAE,SAAS,GAAG,EAAE;QACvB,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;;QAEhB,IAAI,GAAG,GAAG,SAAS,IAAI,GAAG,GAAG,CAAC,EAAE;YAC5B,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAC1B,OAAO;SACV;;QAED,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;QAEhB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,aAAa,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,EAAA,OAAO,EAAA;QAChG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,EAAA,OAAO,EAAA;QAChG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,EAAA,OAAO,EAAA;QAChG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC;KAC/C;;IAED,YAAY,EAAE,SAAS,GAAG,EAAE;QACxB,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;KACtD;;IAED,YAAY,EAAE,SAAS,GAAG,EAAE;QACxB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;KAClC;;IAED,WAAW,EAAE,SAAS,GAAG,EAAE;QACvB,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;QAE7B,IAAI,CAAC,GAAG,EAAE,CAAC;;QAEX,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;;QAExB,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;;QAE9B,IAAI,GAAG,IAAI,IAAI,EAAE,EAAA,sBAAsB,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;;;QAG7D,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC;KACnB;;IAED,UAAU,EAAE,SAAS,GAAG,EAAE;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACjB;;IAED,WAAW,EAAE,SAAS,GAAG,EAAE;QACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACjB;;IAED,UAAU,EAAE,SAAS,MAAM,EAAE;;;QACzB,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,EAAAA,MAAI,CAAC,GAAG,CAACA,MAAI,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,EAAA;KAClE;;IAED,eAAe,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE;QAC/B,IAAI,CAAC,GAAG,EAAE,CAAC;;;QAGX,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;QACxB,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACd,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;;QAE9B,IAAI,GAAG,IAAI,IAAI,EAAE,EAAA,sBAAsB,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAA;;;QAG7D,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC;KACnB;;IAED,YAAY,EAAE,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;KACjC;;IAED,iBAAiB,IAAI,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,iBAAiB,EAAE,GAAG,CAAC,CAAC,IAAI;IAC7F,kBAAkB,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC,GAAG;IAC7F,kBAAkB,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC,GAAG;IAC7F,gBAAgB,KAAK,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,gBAAgB,EAAE,GAAG,CAAC,CAAC,KAAK;IAC7F,iBAAiB,IAAI,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,iBAAiB,EAAE,GAAG,CAAC,CAAC,IAAI;IAC7F,kBAAkB,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC,GAAG;IAC7F,mBAAmB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,mBAAmB,EAAE,GAAG,CAAC,CAAC,EAAE;IAC7F,kBAAkB,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC,GAAG;IAC7F,mBAAmB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,mBAAmB,EAAE,GAAG,CAAC,CAAC,EAAE;;IAE7F,eAAe,EAAE,SAAS,GAAG,EAAE,MAAM,EAAE;QACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;KAC3B;IACD,iBAAiB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QAClC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;KAC1B;IACD,kBAAkB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;KAC3B;IACD,iBAAiB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QAClC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;KAC1B;IACD,kBAAkB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;KAC3B;IACD,gBAAgB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;KACzB;IACD,iBAAiB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QAClC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;KAC1B;IACD,gBAAgB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;KACzB;IACD,eAAe,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KACxB;IACD,gBAAgB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;KACzB;IACD,iBAAiB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE;QAClC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5C;CACJ,CAAC;;AAEF,SAAS,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IAClC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG;QACX,CAAC,EAAE,CAAC,CAAC;;IAET,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAA;IAC7E,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAA;IAC7E,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAA;IAC7E,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAA;IAC7E,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAA;IAC7E,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAA,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAA;;IAE7E,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;CAC7D;;AAED,SAAS,aAAa,CAAC,GAAG,EAAE;IACxB,OAAO,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK;QACzB,GAAG,CAAC,UAAU,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;CAChD;;AAED,SAAS,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE;IAChC,IAAI,QAAQ,EAAE;QACV,OAAO,IAAI,GAAG,WAAW,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;KAC3C;;IAED,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,WAAW,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;CACrD;;AAED,SAAS,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;IAC9B,IAAI,GAAG,EAAE,IAAI,CAAC;;IAEd,IAAI,GAAG,IAAI,CAAC,EAAE;QACV,GAAG,IAAI,CAAC,GAAG,GAAG,WAAW,IAAI,CAAC,CAAC;QAC/B,IAAI,GAAG,CAAC,GAAG,GAAG,WAAW,IAAI,CAAC,CAAC;KAClC,MAAM;QACH,GAAG,IAAI,EAAE,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC;QAC7B,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC;;QAE7B,IAAI,GAAG,GAAG,UAAU,EAAE;YAClB,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;SACvB,MAAM;YACH,GAAG,GAAG,CAAC,CAAC;YACR,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;SACzB;KACJ;;IAED,IAAI,GAAG,IAAI,mBAAmB,IAAI,GAAG,GAAG,CAAC,mBAAmB,EAAE;QAC1D,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC9D;;IAED,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;;IAEhB,iBAAiB,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IAClC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;CACjC;;AAED,SAAS,iBAAiB,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE;IACvC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IACnD,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IACnD,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IACnD,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IACnD,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC;CACnC;;AAED,SAAS,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE;IACnC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC;;IAE7B,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAA,OAAO,EAAA;IAClF,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAA,OAAO,EAAA;IAClF,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAA,OAAO,EAAA;IAClF,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAA,OAAO,EAAA;IAClF,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAA,OAAO,EAAA;IAClF,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;CACrC;;AAED,SAAS,sBAAsB,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE;IAChD,IAAI,QAAQ;QACR,GAAG,IAAI,MAAM,GAAG,CAAC;QACjB,GAAG,IAAI,QAAQ,GAAG,CAAC;QACnB,GAAG,IAAI,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;;;IAGrE,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAA;CACpF;;AAED,SAAS,iBAAiB,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,IAAI;AAC1G,SAAS,kBAAkB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,GAAG;AAC1G,SAAS,gBAAgB,CAAC,GAAG,EAAE,GAAG,KAAK,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,KAAK;AAC1G,SAAS,iBAAiB,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,IAAI;AAC1G,SAAS,kBAAkB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,GAAG;AAC1G,SAAS,kBAAkB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,GAAG;AAC1G,SAAS,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE;AAC1G,SAAS,kBAAkB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,GAAG;AAC1G,SAAS,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,EAAE;;;;AAI1G,SAAS,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE;IAC1B,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;SACZ,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;SAClB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;SACnB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;CAClC;;AAED,SAAS,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IAC/B,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IACf,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;IAC3B,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC;IAC5B,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC;CAC/B;;AAED,SAAS,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE;IACzB,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;SACZ,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;SAClB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;SACnB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;CAC5B;;AAED,SAAS,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IAC7B,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,CAAC,GAAG,GAAG,CAAC;;IAEZ,OAAO,CAAC,GAAG,GAAG,EAAE;QACZ,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAChB,IAAI,CAAC,GAAG,IAAI,CAAC;QACb,IAAI,gBAAgB;YAChB,EAAE,GAAG,IAAI,GAAG,CAAC;YACb,EAAE,GAAG,IAAI,GAAG,CAAC;YACb,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;;QAEtB,IAAI,CAAC,GAAG,gBAAgB,GAAG,GAAG,EAAE,EAAA,MAAM,EAAA;;QAEtC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEf,IAAI,gBAAgB,KAAK,CAAC,EAAE;YACxB,IAAI,EAAE,GAAG,IAAI,EAAE;gBACX,CAAC,GAAG,EAAE,CAAC;aACV;SACJ,MAAM,IAAI,gBAAgB,KAAK,CAAC,EAAE;YAC/B,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,EAAE;gBACtB,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;gBACrC,IAAI,CAAC,IAAI,IAAI,EAAE;oBACX,CAAC,GAAG,IAAI,CAAC;iBACZ;aACJ;SACJ,MAAM,IAAI,gBAAgB,KAAK,CAAC,EAAE;YAC/B,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,EAAE;gBAC9C,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;gBACzD,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;oBAC5C,CAAC,GAAG,IAAI,CAAC;iBACZ;aACJ;SACJ,MAAM,IAAI,gBAAgB,KAAK,CAAC,EAAE;YAC/B,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,EAAE;gBACtE,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC/E,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,QAAQ,EAAE;oBAC9B,CAAC,GAAG,IAAI,CAAC;iBACZ;aACJ;SACJ;;QAED,IAAI,CAAC,KAAK,IAAI,EAAE;YACZ,CAAC,GAAG,MAAM,CAAC;YACX,gBAAgB,GAAG,CAAC,CAAC;;SAExB,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE;YACnB,CAAC,IAAI,OAAO,CAAC;YACb,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;YACtD,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC;SAC1B;;QAED,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,IAAI,gBAAgB,CAAC;KACzB;;IAED,OAAO,GAAG,CAAC;CACd;;AAED,SAAS,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;;QAEtB,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE;YAC1B,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,GAAG,MAAM,EAAE;oBACZ,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;oBAClB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;oBAClB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;oBAClB,IAAI,GAAG,CAAC,CAAC;oBACT,SAAS;iBACZ,MAAM;oBACH,CAAC,GAAG,IAAI,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;oBAC/C,IAAI,GAAG,IAAI,CAAC;iBACf;aACJ,MAAM;gBACH,IAAI,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE;oBACtC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;oBAClB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;oBAClB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;iBACrB,MAAM;oBACH,IAAI,GAAG,CAAC,CAAC;iBACZ;gBACD,SAAS;aACZ;SACJ,MAAM,IAAI,IAAI,EAAE;YACb,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;YAClB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;YAClB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;YAClB,IAAI,GAAG,IAAI,CAAC;SACf;;QAED,IAAI,CAAC,GAAG,IAAI,EAAE;YACV,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;SAClB,MAAM;YACH,IAAI,CAAC,GAAG,KAAK,EAAE;gBACX,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC;aAChC,MAAM;gBACH,IAAI,CAAC,GAAG,OAAO,EAAE;oBACb,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC;iBAChC,MAAM;oBACH,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;oBAC9B,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;iBACvC;gBACD,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;aACvC;YACD,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;SAChC;KACJ;IACD,OAAO,GAAG,CAAC;CACd;;ADvmBD,WAAc,GAAGD,OAAK,CAAC;;AAEvB,SAASA,OAAK,CAAC,CAAC,EAAE,CAAC,EAAE;IACjB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;CACd;;AAEDA,OAAK,CAAC,SAAS,GAAG;IACd,KAAK,EAAE,WAAW,EAAE,OAAO,IAAIA,OAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEvD,GAAG,MAAM,SAAS,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;IACzD,GAAG,MAAM,SAAS,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;IACzD,IAAI,KAAK,SAAS,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;IACzD,GAAG,MAAM,SAAS,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;IACzD,MAAM,GAAG,SAAS,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG;IACzD,OAAO,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;IACzD,IAAI,KAAK,WAAW,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;IACpD,IAAI,KAAK,WAAW,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;IACpD,KAAK,IAAI,WAAW,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE;;IAErD,GAAG,EAAE,WAAW;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;KACvD;;IAED,MAAM,EAAE,SAAS,CAAC,EAAE;QAChB,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;eACd,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACzB;;IAED,IAAI,EAAE,SAAS,CAAC,EAAE;QACd,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;KACrC;;IAED,OAAO,EAAE,SAAS,CAAC,EAAE;QACjB,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACjB,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACtB,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;KAC5B;;IAED,KAAK,EAAE,WAAW;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;KACrC;;IAED,OAAO,EAAE,SAAS,CAAC,EAAE;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACjD;;IAED,SAAS,EAAE,SAAS,CAAC,EAAE;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;;;IAGD,YAAY,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE;QACzB,OAAO,IAAI,CAAC,KAAK;YACb,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC;YACvB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAChC;;IAED,QAAQ,EAAE,SAAS,CAAC,EAAE;QAClB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACjC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,OAAO,IAAI,CAAC;KACf;;IAED,IAAI,EAAE,SAAS,CAAC,EAAE;QACd,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,OAAO,IAAI,CAAC;KACf;;IAED,IAAI,EAAE,SAAS,CAAC,EAAE;QACd,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,OAAO,IAAI,CAAC;KACf;;IAED,KAAK,EAAE,SAAS,CAAC,EAAE;QACf,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC;KACf;;IAED,IAAI,EAAE,SAAS,CAAC,EAAE;QACd,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC;KACf;;IAED,KAAK,EAAE,WAAW;QACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;KACf;;IAED,KAAK,EAAE,WAAW;QACd,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAChB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC;KACf;;IAED,OAAO,EAAE,SAAS,KAAK,EAAE;QACrB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YACrB,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YACrB,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;YAC/B,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,OAAO,IAAI,CAAC;KACf;;IAED,MAAM,EAAE,WAAW;QACf,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC;KACf;CACJ,CAAC;;;AAGFA,OAAK,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE;IACzB,IAAI,CAAC,YAAYA,OAAK,EAAE;QACpB,OAAO,CAAC,CAAC;KACZ;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;QAClB,OAAO,IAAIA,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAChC;IACD,OAAO,CAAC,CAAC;CACZ,CAAC;;ADhIF,IAAI,KAAK,GAAGF,OAAyB,CAAC;;AAEtC,qBAAc,GAAGC,mBAAiB,CAAC;;AAEnC,SAASA,mBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;;IAEvD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;;;IAGd,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAChB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;;IAEtB,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;CAC1C;;AAED,SAAS,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;IACpC,IAAI,GAAG,IAAI,CAAC,EAAE,EAAA,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,EAAA;SACvC,IAAI,GAAG,IAAI,CAAC,EAAE,EAAA,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,EAAA;SACpC,IAAI,GAAG,IAAI,CAAC,EAAE,EAAA,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,EAAA;SAC9C,IAAI,GAAG,IAAI,CAAC,EAAE,EAAA,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,EAAA;CAClD;;AAED,SAAS,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE;IAC3B,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC;;IAErC,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;QAClB,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;YACrC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;KACnC;CACJ;;AAEDA,mBAAiB,CAAC,KAAK,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;;AAExEA,mBAAiB,CAAC,SAAS,CAAC,YAAY,GAAG,WAAW;IAClD,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;;IAEzB,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE,GAAG,GAAG,CAAC,GAAG;QAChC,GAAG,GAAG,CAAC;QACP,MAAM,GAAG,CAAC;QACV,CAAC,GAAG,CAAC;QACL,CAAC,GAAG,CAAC;QACL,KAAK,GAAG,EAAE;QACV,IAAI,CAAC;;IAET,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;QAClB,IAAI,CAAC,MAAM,EAAE;YACT,IAAI,MAAM,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;YAC9B,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;YACnB,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;SACxB;;QAED,MAAM,EAAE,CAAC;;QAET,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;YACxB,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YACvB,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;;YAEvB,IAAI,GAAG,KAAK,CAAC,EAAE;gBACX,IAAI,IAAI,EAAE,EAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAA;gBAC3B,IAAI,GAAG,EAAE,CAAC;aACb;;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;SAE9B,MAAM,IAAI,GAAG,KAAK,CAAC,EAAE;;;YAGlB,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;aAC9B;;SAEJ,MAAM;YACH,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,GAAG,CAAC,CAAC;SAC7C;KACJ;;IAED,IAAI,IAAI,EAAE,EAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAA;;IAE3B,OAAO,KAAK,CAAC;CAChB,CAAC;;AAEFA,mBAAiB,CAAC,SAAS,CAAC,IAAI,GAAG,WAAW;IAC1C,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;;IAEzB,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE,GAAG,GAAG,CAAC,GAAG;QAChC,GAAG,GAAG,CAAC;QACP,MAAM,GAAG,CAAC;QACV,CAAC,GAAG,CAAC;QACL,CAAC,GAAG,CAAC;QACL,EAAE,GAAG,QAAQ;QACb,EAAE,GAAG,CAAC,QAAQ;QACd,EAAE,GAAG,QAAQ;QACb,EAAE,GAAG,CAAC,QAAQ,CAAC;;IAEnB,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;QAClB,IAAI,CAAC,MAAM,EAAE;YACT,IAAI,MAAM,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;YAC9B,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;YACnB,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;SACxB;;QAED,MAAM,EAAE,CAAC;;QAET,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;YACxB,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YACvB,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAA,EAAE,GAAG,CAAC,CAAC,EAAA;YACnB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAA,EAAE,GAAG,CAAC,CAAC,EAAA;YACnB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAA,EAAE,GAAG,CAAC,CAAC,EAAA;YACnB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAA,EAAE,GAAG,CAAC,CAAC,EAAA;;SAEtB,MAAM,IAAI,GAAG,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,GAAG,CAAC,CAAC;SAC7C;KACJ;;IAED,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;CAC3B,CAAC;;AAEFA,mBAAiB,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IACtD,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACnC,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;QACpB,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;QACpB,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE;QAC5B,IAAI,GAAGA,mBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;QACzC,CAAC,EAAE,CAAC,CAAC;;IAET,SAAS,OAAO,CAAC,IAAI,EAAE;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC;YACpD,IAAI,CAAC,CAAC,CAAC,GAAG;gBACN,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG;gBAC7B,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE;aAC/D,CAAC;SACL;KACJ;;IAED,QAAQ,IAAI,CAAC,IAAI;IACjB,KAAK,CAAC;QACF,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5B;QACD,MAAM,GAAG,MAAM,CAAC;QAChB,OAAO,CAAC,MAAM,CAAC,CAAC;QAChB,MAAM;;IAEV,KAAK,CAAC;QACF,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACtB;QACD,MAAM;;IAEV,KAAK,CAAC;QACF,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QAC/B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzB;SACJ;QACD,MAAM;KACT;;IAED,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACtB,MAAM;QACH,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC;KACzB;;IAED,IAAI,MAAM,GAAG;QACT,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE;YACN,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,MAAM;SACtB;QACD,UAAU,EAAE,IAAI,CAAC,UAAU;KAC9B,CAAC;;IAEF,IAAI,IAAI,IAAI,IAAI,EAAE;QACd,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;KACvB;;IAED,OAAO,MAAM,CAAC;CACjB,CAAC;;;;AAIF,SAAS,aAAa,CAAC,KAAK,EAAE;IAC1B,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;;IAEvB,IAAI,GAAG,IAAI,CAAC,EAAE,EAAA,OAAO,CAAC,KAAK,CAAC,CAAC,EAAA;;IAE7B,IAAI,QAAQ,GAAG,EAAE;QACb,OAAO;QACP,GAAG,CAAC;;IAER,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAC1B,IAAI,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,IAAI,KAAK,CAAC,EAAE,EAAA,SAAS,EAAA;;QAEzB,IAAI,GAAG,KAAK,SAAS,EAAE,EAAA,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,EAAA;;QAEtC,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,EAAE;YAClB,IAAI,OAAO,EAAE,EAAA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAA;YACpC,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;SAExB,MAAM;YACH,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1B;KACJ;IACD,IAAI,OAAO,EAAE,EAAA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAA;;IAEpC,OAAO,QAAQ,CAAC;CACnB;;AAED,SAAS,UAAU,CAAC,IAAI,EAAE;IACtB,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE;QACtE,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;KACxC;IACD,OAAO,GAAG,CAAC;CACd;;ADtOD,IAAIA,mBAAiB,GAAGD,iBAAiC,CAAC;;AAE1D,mBAAc,GAAGF,iBAAe,CAAC;;AAEjC,SAASA,iBAAe,CAAC,GAAG,EAAE,GAAG,EAAE;;IAE/B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;;;IAGhB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IAClB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;;IAEpB,GAAG,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;;IAErC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;CACvC;;AAED,SAAS,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;IAChC,IAAI,GAAG,KAAK,EAAE,EAAE,EAAA,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,EAAA;SAC5C,IAAI,GAAG,KAAK,CAAC,EAAE,EAAA,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,EAAA;SAC7C,IAAI,GAAG,KAAK,CAAC,EAAE,EAAA,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,EAAA;SAC/C,IAAI,GAAG,KAAK,CAAC,EAAE,EAAA,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAA;SAC7C,IAAI,GAAG,KAAK,CAAC,EAAE,EAAA,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,EAAA;SAClD,IAAI,GAAG,KAAK,CAAC,EAAE,EAAA,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAA;CACjE;;AAED,SAAS,gBAAgB,CAAC,GAAG,EAAE;IAC3B,IAAI,KAAK,GAAG,IAAI;QACZ,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC;;IAErC,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;QAClB,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;;QAEhC,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,UAAU,EAAE;YAChC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,SAAS,EAAE;YAC3B,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,UAAU,EAAE;YAC5B,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,YAAY,EAAE;YAC9B,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,UAAU,EAAE;YAC5B,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE;YAC7B,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC;KAC5C;;IAED,OAAO,KAAK,CAAC;CAChB;;;AAGDA,iBAAe,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,EAAE;IAC5C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAA,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,EAAA;;IAExF,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;IAElC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;IACjD,OAAO,IAAIG,mBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;CACvF,CAAC;;AD1DF,IAAIH,iBAAe,GAAGE,eAA4B,CAAC;;AAEnD,cAAc,GAAGD,YAAU,CAAC;;AAE5B,SAASA,YAAU,CAAC,GAAG,EAAE,GAAG,EAAE;IAC1B,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;CACnD;;AAED,SAAS,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;IAChC,IAAI,GAAG,KAAK,CAAC,EAAE;QACX,IAAI,KAAK,GAAG,IAAID,iBAAe,CAAC,GAAG,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACjE,IAAI,KAAK,CAAC,MAAM,EAAE,EAAA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,EAAA;KAChD;CACJ;;ADfD,iBAA4BD,UAA8B,CAAC,AAC3D,AAAmC,AAAqC,AACxE,AAAiC,AAAmC;;ADEpE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;;CAE3C,OAAO,EAAE;;;;;;;;EAQR,UAAU,EAAE,KAAK;;;;EAIjB,YAAY,EAAE,EAAE;EAChB;;CAED,UAAU,EAAE,SAAS,GAAG,EAAE,OAAO,EAAE;;;EAGlC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;EAChB,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;EACtD;;;;CAID,MAAM,EAAE,SAAS,GAAG,EAAE,QAAQ,EAAE;EAC/B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;;EAEhB,IAAI,CAAC,QAAQ,EAAE;GACd,IAAI,CAAC,MAAM,EAAE,CAAC;GACd;;EAED,OAAO,IAAI,CAAC;EACZ;;CAED,aAAa,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa;;CAElD,qBAAqB,EAAE,SAAS,MAAM,EAAE;EACvC,IAAI,IAAI,GAAG;GACV,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;GAC7B,CAAC,EAAE,MAAM,CAAC,CAAC;GACX,CAAC,EAAE,MAAM,CAAC,CAAC;GACX,CAAC,EAAE,MAAM,CAAC,CAAC;;GAEX,CAAC;EACF,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;GACjD,IAAI,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;GACvD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;IACrB,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;IACtB;GACD,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;GACvB;;EAED,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEvE,OAAO,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,QAAQ,CAAC;;GAEvE,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;IACjB,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACnB;;GAED,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,UAAU,IAAI,EAAE;;;IAG5C,IAAI,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;IAC9B,OAAO,IAAI,OAAO,CAAC,SAAS,OAAO,CAAC;KACnC,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAW;;;;MAI7C,IAAI,GAAG,GAAG,IAAID,KAAG,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;;MAEnC,OAAO,OAAO,CAAC,IAAI,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;;MAEtC,CAAC,CAAC;KACH,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KAC/B,CAAC,CAAC;IACH,CAAC,CAAC;GACH,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC;;;;;;GAMrB,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE;IAClC,IAAI,KAAK,GAAG,EAAE,CAAC;;IAEf,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;KACnD,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;KAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;KACpC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACjB;;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxC;;GAED,OAAO,IAAI,CAAC;GACZ,CAAC,CAAC;EACH;CACD,CAAC,CAAC;;;;;AAKH,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,UAAU,GAAG,EAAE,OAAO,EAAE;CAC/C,OAAO,IAAI,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;CAC/C,CAAC;;;;AD5JF;;;;AAIA,AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;;CAEzC,OAAO,EAAE;;;;;;;;;EASR,mBAAmB,EAAE,QAAQ;;EAE7B,MAAM,EAAE,IAAI;EACZ,OAAO,EAAE,EAAE;EACX;;CAED,UAAU,EAAE,SAAS,OAAO,EAAE,OAAO,EAAE;EACtC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;;;EAItD,IAAI,OAAO,GAAG,EAAE,CAAC;EACjB,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;GAC3B,IAAI,CAAC,KAAK,iBAAiB;IAC1B,CAAC,KAAK,uBAAuB;IAC7B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,UAAU;KACtC;IACD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7B;GACD;;;EAGD,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC;;;EAGtC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;;EAEtD;;;CAGD,qBAAqB,EAAE,SAAS,MAAM,EAAE;;EAEvC,IAAI,KAAK,GAAG,IAAI,CAAC;;EAEjB,IAAI,CAAC,GAAG,IAAI,OAAO,EAAE,SAAS,aAAa,CAAC,GAAG,EAAE;GAChD,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE;IAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM;QACb,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;QAC5B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;QAC5B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG;;KAElC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;KACZ,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KACnD;IACD,CAAC,CAAC;GACH,CAAC,CAAC;;EAEH,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;;EAE1C,OAAO,CAAC,CAAC;EACT;;CAED,CAAC,CAAC;;;AAGH,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,UAAU,OAAO,EAAE,OAAO,EAAE;CACjD,OAAO,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CACjD,CAAC;;AD3GF,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;;CAE/B,UAAU,EAAE,UAAU,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE;EACnD,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;EAClD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;EAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;;EAEtB,IAAI,CAAC,cAAc,EAAE,CAAC;EACtB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACpD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACrD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;EAClB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;EACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;;EAErB,IAAI,OAAO,CAAC,WAAW,EAAE;;GAExB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;GAC7C;EACD;;CAED,QAAQ,EAAE,WAAW;EACpB,OAAO,IAAI,CAAC,UAAU,CAAC;EACvB;;CAED,YAAY,EAAE,WAAW;EACxB,OAAO,IAAI,CAAC,UAAU,CAAC;EACvB;;CAED,SAAS,EAAE,WAAW;EACrB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;EAChF;;CAED,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO;;CAErB,KAAK,EAAE,SAAS,GAAG,EAAE;EACpB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;EAChB;;CAED,UAAU,EAAE,UAAU,GAAG,EAAE;EAC1B,OAAO,IAAI,CAAC,IAAI,CAAC;EACjB;;CAED,QAAQ,EAAE,UAAU,CAAC,EAAE;EACtB,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,CAAC;;EAEhG,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE;GAC5B,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;GACzB,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;IAClG,YAAY,GAAG,KAAK,CAAC;IACrB;GACD;EACD,IAAI,YAAY,GAAG;GAClB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;GACvB,IAAI,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;GACnC;EACD;;CAED,YAAY,EAAE,UAAU,CAAC,EAAE;EAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE;;EAEtF,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;EAC3E,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;EACjC;;;;;CAKD,WAAW,EAAE,UAAU,KAAK,EAAE;EAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE;;EAE/B,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI;MACzB,OAAO,GAAG,IAAI,CAAC,OAAO;MACtB,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAM,GAAG,OAAO,CAAC,UAAU;YACrB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;MACpC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;MACjC,GAAG,GAAG,IAAI,CAAC,IAAI;MACf,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;;EAE5B,IAAI,GAAG,CAAC,QAAQ,EAAE;GACjB,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;GAC7C,MAAM;GACN,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW;IACrC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;GACH;;EAED,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;EAC7C;CACD,CAAC,CAAC;;;AAGH,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,SAAS,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;CAClD,OAAO,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;CACpD,CAAA;;AD/FD,yCAAyC,AAEzC,AACA,AACA,AACA,AACA,AAAgD"}
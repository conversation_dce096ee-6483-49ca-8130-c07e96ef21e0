# @turf/boolean-point-on-line

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanPointOnLine

Returns true if a point is on a line. Accepts a optional parameter to ignore the start and end vertices of the linestring.

**Parameters**

-   `pt` **[Coord][1]** GeoJSON Point
-   `line` **[Feature][2]&lt;[LineString][3]>** GeoJSON LineString
-   `options` **[Object][4]** Optional parameters (optional, default `{}`)
    -   `options.ignoreEndVertices` **[boolean][5]** whether to ignore the start and end vertices. (optional, default `false`)

**Examples**

```javascript
var pt = turf.point([0, 0]);
var line = turf.lineString([[-1, -1],[1, 1],[1.5, 2.2]]);
var isPointOnLine = turf.booleanPointOnLine(pt, line);
//=true
```

Returns **[boolean][5]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/boolean-point-on-line
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```

# @turf/nearest-point-on-line

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## nearestPointOnLine

Takes a [Point][1] and a [LineString][2] and calculates the closest Point on the (Multi)LineString.

**Parameters**

-   `lines` **([Geometry][3] \| [Feature][4]&lt;([LineString][5] \| [MultiLineString][6])>)** lines to snap to
-   `pt` **([Geometry][3] \| [Feature][4]&lt;[Point][7]> | [Array][8]&lt;[number][9]>)** point to snap from
-   `options` **[Object][10]** Optional parameters (optional, default `{}`)
    -   `options.units` **[string][11]** can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)

**Examples**

```javascript
var line = turf.lineString([
    [-77.031669, 38.878605],
    [-77.029609, 38.881946],
    [-77.020339, 38.884084],
    [-77.025661, 38.885821],
    [-77.021884, 38.889563],
    [-77.019824, 38.892368]
]);
var pt = turf.point([-77.037076, 38.884017]);

var snapped = turf.nearestPointOnLine(line, pt, {units: 'miles'});

//addToMap
var addToMap = [line, pt, snapped];
snapped.properties['marker-color'] = '#00f';
```

Returns **[Feature][4]&lt;[Point][7]>** closest point on the `line` to `point`. The properties object will contain three values: `index`: closest point was found on nth line part, `dist`: distance between pt and the closest point, `location`: distance along the line between start and the closest point.

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[3]: https://tools.ietf.org/html/rfc7946#section-3.1

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[9]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[10]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[11]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/nearest-point-on-line
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```

{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["type Comparator<T> = (a: T, b: T) => number;\ntype Predicate<T> = (value: T) => boolean;\n\nclass SplayTreeNode<K, Node extends SplayTreeNode<K, Node>> {\n    readonly key: K;\n\n    left: Node | null = null;\n    right: Node | null = null;\n\n    constructor(key: K) {\n        this.key = key;\n    }\n}\n\nclass SplayTreeSetNode<K> extends SplayTreeNode<K, SplayTreeSetNode<K>> {\n    constructor(key: K) {\n        super(key);\n    }\n}\n\nclass SplayTreeMapNode<K, V> extends SplayTreeNode<K, SplayTreeMapNode<K, V>> {\n    readonly value: V;\n\n    constructor(key: K, value: V) {\n        super(key);\n        this.value = value;\n    }\n\n    replaceValue(value: V) {\n        const node = new SplayTreeMapNode(this.key, value);\n        node.left = this.left;\n        node.right = this.right;\n        return node;\n    }\n}\n\nabstract class SplayTree<K, Node extends SplayTreeNode<K, Node>> {\n    protected abstract root: Node | null;\n\n    public size = 0;\n\n    protected modificationCount = 0;\n\n    protected splayCount = 0;\n\n    protected abstract compare: Comparator<K>;\n\n    protected abstract validKey: Predicate<unknown>;\n\n    protected splay(key: K) {\n        const root = this.root;\n        if (root == null) {\n            this.compare(key, key);\n            return -1;\n        }\n\n        let right: Node | null = null;\n        let newTreeRight: Node | null = null;\n        let left: Node | null = null;\n        let newTreeLeft: Node | null = null;\n        let current = root;\n        const compare = this.compare;\n        let comp: number;\n        while (true) {\n            comp = compare(current.key, key);\n            if (comp > 0) {\n                let currentLeft = current.left;\n                if (currentLeft == null) break;\n                comp = compare(currentLeft.key, key);\n                if (comp > 0) {\n                    current.left = currentLeft.right;\n                    currentLeft.right = current;\n                    current = currentLeft;\n                    currentLeft = current.left;\n                    if (currentLeft == null) break;\n                }\n                if (right == null) {\n                    newTreeRight = current;\n                } else {\n                    right.left = current;\n                }\n                right = current;\n                current = currentLeft;\n            } else if (comp < 0) {\n                let currentRight = current.right;\n                if (currentRight == null) break;\n                comp = compare(currentRight.key, key);\n                if (comp < 0) {\n                    current.right = currentRight.left;\n                    currentRight.left = current;\n                    current = currentRight;\n                    currentRight = current.right;\n                    if (currentRight == null) break;\n                }\n                if (left == null) {\n                    newTreeLeft = current;\n                } else {\n                    left.right = current;\n                }\n                left = current;\n                current = currentRight;\n            } else {\n                break;\n            }\n        }\n        if (left != null) {\n            left.right = current.left;\n            current.left = newTreeLeft;\n        }\n        if (right != null) {\n            right.left = current.right;\n            current.right = newTreeRight;\n        }\n        if (this.root !== current) {\n            this.root = current;\n            this.splayCount++;\n        }\n        return comp;\n    }\n\n    protected splayMin(node: Node) {\n        let current = node;\n        let nextLeft = current.left;\n        while (nextLeft != null) {\n            const left = nextLeft;\n            current.left = left.right;\n            left.right = current;\n            current = left;\n            nextLeft = current.left;\n        }\n        return current;\n    }\n\n    protected splayMax(node: Node) {\n        let current = node;\n        let nextRight = current.right;\n        while (nextRight != null) {\n            const right = nextRight;\n            current.right = right.left;\n            right.left = current;\n            current = right;\n            nextRight = current.right;\n        }\n        return current;\n    }\n\n    protected _delete(key: K) {\n        if (this.root == null) return null;\n        const comp = this.splay(key);\n        if (comp != 0) return null;\n        let root = this.root;\n        const result = root;\n        const left = root.left;\n        this.size--;\n        if (left == null) {\n            this.root = root.right;\n        } else {\n            const right = root.right;\n            root = this.splayMax(left);\n\n            root.right = right;\n            this.root = root;\n        }\n        this.modificationCount++;\n        return result;\n    }\n\n    protected addNewRoot(node: Node, comp: number) {\n        this.size++;\n        this.modificationCount++;\n        const root = this.root;\n        if (root == null) {\n            this.root = node;\n            return;\n        }\n        if (comp < 0) {\n            node.left = root;\n            node.right = root.right;\n            root.right = null;\n        } else {\n            node.right = root;\n            node.left = root.left;\n            root.left = null;\n        }\n        this.root = node;\n    }\n\n    protected _first() {\n        const root = this.root;\n        if (root == null) return null;\n        this.root = this.splayMin(root);\n        return this.root;\n    }\n\n    protected _last() {\n        const root = this.root;\n        if (root == null) return null;\n        this.root = this.splayMax(root);\n        return this.root;\n    }\n\n    public clear() {\n        this.root = null;\n        this.size = 0;\n        this.modificationCount++;\n    }\n\n    public has(key: unknown) {\n        return this.validKey(key) && this.splay(key as K) == 0;\n    }\n\n    protected defaultCompare(): Comparator<K> {\n        return (a: K, b: K) => a < b ? -1 : a > b ? 1 : 0;\n    }\n\n    protected wrap(): SplayTreeWrapper<K, Node> {\n        return {\n            getRoot: () => { return this.root },\n            setRoot: (root) => { this.root = root },\n            getSize: () => { return this.size },\n            getModificationCount: () => { return this.modificationCount },\n            getSplayCount: () => { return this.splayCount },\n            setSplayCount: (count) => { this.splayCount = count },\n            splay: (key) => { return this.splay(key) },\n            has: (key) => { return this.has(key) },\n        };\n    }\n}\n\nexport class SplayTreeMap<K, V> extends SplayTree<K, SplayTreeMapNode<K, V>> implements Iterable<[K, V]>, Map<K, V> {\n    protected root: SplayTreeMapNode<K, V> | null = null;\n\n    protected compare: Comparator<K>;\n    protected validKey: Predicate<unknown>;\n\n    constructor(compare?: Comparator<K>, isValidKey?: Predicate<unknown>) {\n        super();\n        this.compare = compare ?? this.defaultCompare();\n        this.validKey = isValidKey ?? ((a: unknown) => a != null && a != undefined);\n    }\n\n    delete(key: unknown) {\n        if (!this.validKey(key)) return false;\n        return this._delete(key as K) != null;\n    }\n\n    forEach(f: (value: V, key: K, map: Map<K, V>) => void) {\n        const nodes: Iterator<[K, V]> = new SplayTreeMapEntryIterableIterator<K, V>(this.wrap());\n        let result: IteratorResult<[K, V]>;\n        while (result = nodes.next(), !result.done) {\n            f(result.value[1], result.value[0], this);\n        }\n    }\n\n    get(key: unknown): V | undefined {\n        if (!this.validKey(key)) return undefined;\n        if (this.root != null) {\n            const comp = this.splay(key as K);\n            if (comp == 0) {\n                return this.root!.value;\n            }\n        }\n        return undefined;\n    }\n\n    hasValue(value: unknown) {\n        const initialSplayCount = this.splayCount;\n        const visit = (node: SplayTreeMapNode<K, V> | null) => {\n            while (node != null) {\n                if (node.value == value) return true;\n                if (initialSplayCount != this.splayCount) {\n                    throw \"Concurrent modification during iteration.\";\n                }\n                if (node.right != null && visit(node.right)) {\n                    return true;\n                }\n                node = node.left;\n            }\n            return false;\n        }\n\n        return visit(this.root);\n    }\n\n    set(key: K, value: V) {\n        const comp = this.splay(key);\n        if (comp == 0) {\n            this.root = this.root!.replaceValue(value);\n            this.splayCount += 1;\n            return this;\n        }\n        this.addNewRoot(new SplayTreeMapNode(key, value), comp);\n        return this;\n    }\n\n    setAll(other: Map<K, V>) {\n        other.forEach((value: V, key: K) => {\n            this.set(key, value);\n        });\n    }\n\n    setIfAbsent(key: K, ifAbsent: () => V) {\n        let comp = this.splay(key);\n        if (comp == 0) {\n            return this.root!.value;\n        }\n        const modificationCount = this.modificationCount;\n        const splayCount = this.splayCount;\n        const value = ifAbsent();\n        if (modificationCount != this.modificationCount) {\n            throw \"Concurrent modification during iteration.\";\n        }\n        if (splayCount != this.splayCount) {\n            comp = this.splay(key);\n        }\n        this.addNewRoot(new SplayTreeMapNode(key, value), comp);\n        return value;\n    }\n\n    isEmpty() {\n        return this.root == null;\n    }\n\n    isNotEmpty() {\n        return !this.isEmpty();\n    }\n\n    firstKey() {\n        if (this.root == null) return null;\n        return this._first()!.key;\n    }\n\n    lastKey() {\n        if (this.root == null) return null;\n        return this._last()!.key;\n    }\n\n    lastKeyBefore(key: K) {\n        if (key == null) throw \"Invalid arguments(s)\";\n        if (this.root == null) return null;\n        const comp = this.splay(key);\n        if (comp < 0) return this.root!.key;\n        let node: SplayTreeMapNode<K, V> | null = this.root!.left;\n        if (node == null) return null;\n        let nodeRight = node.right;\n        while (nodeRight != null) {\n            node = nodeRight;\n            nodeRight = node.right;\n        }\n        return node!.key;\n    }\n\n    firstKeyAfter(key: K) {\n        if (key == null) throw \"Invalid arguments(s)\";\n        if (this.root == null) return null;\n        const comp = this.splay(key);\n        if (comp > 0) return this.root!.key;\n        let node: SplayTreeMapNode<K, V> | null = this.root!.right;\n        if (node == null) return null;\n        let nodeLeft = node.left;\n        while (nodeLeft != null) {\n            node = nodeLeft;\n            nodeLeft = node.left;\n        }\n        return node!.key;\n    }\n\n    update(key: K, update: (value: V) => V, ifAbsent?: () => V) {\n        let comp = this.splay(key);\n        if (comp == 0) {\n            const modificationCount = this.modificationCount;\n            const splayCount = this.splayCount;\n            const newValue = update(this.root!.value);\n            if (modificationCount != this.modificationCount) {\n                throw \"Concurrent modification during iteration.\";\n            }\n            if (splayCount != this.splayCount) {\n                this.splay(key);\n            }\n            this.root = this.root!.replaceValue(newValue);\n            this.splayCount += 1;\n            return newValue;\n        }\n        if (ifAbsent != null) {\n            const modificationCount = this.modificationCount;\n            const splayCount = this.splayCount;\n            const newValue = ifAbsent();\n            if (modificationCount != this.modificationCount) {\n                throw \"Concurrent modification during iteration.\";\n            }\n            if (splayCount != this.splayCount) {\n                comp = this.splay(key);\n            }\n            this.addNewRoot(new SplayTreeMapNode(key, newValue), comp);\n            return newValue;\n        }\n        throw \"Invalid argument (key): Key not in map.\"\n    }\n\n    updateAll(update: (key: K, value: V) => V) {\n        const root = this.root;\n        if (root == null) return;\n        const iterator = new SplayTreeMapEntryIterableIterator(this.wrap());\n        let node: IteratorResult<[K, V]>;\n        while (node = iterator.next(), !node.done) {\n            const newValue = update(...node.value);\n            iterator.replaceValue(newValue);\n        }\n    }\n\n    keys(): IterableIterator<K> {\n        return new SplayTreeKeyIterableIterator<K, SplayTreeMapNode<K, V>>(this.wrap());\n    }\n\n    values(): IterableIterator<V> {\n        return new SplayTreeValueIterableIterator<K, V>(this.wrap());\n    }\n\n    entries(): IterableIterator<[K, V]> {\n        return this[Symbol.iterator]();\n    }\n\n    [Symbol.iterator](): IterableIterator<[K, V]> {\n        return new SplayTreeMapEntryIterableIterator<K, V>(this.wrap());\n    }\n\n    [Symbol.toStringTag] = '[object Map]'\n}\n\nexport class SplayTreeSet<E> extends SplayTree<E, SplayTreeSetNode<E>> implements Iterable<E>, Set<E> {\n    protected root: SplayTreeSetNode<E> | null = null;\n\n    protected compare: Comparator<E>;\n    protected validKey: Predicate<unknown>;\n\n    constructor(compare?: Comparator<E>, isValidKey?: Predicate<unknown>) {\n        super();\n        this.compare = compare ?? this.defaultCompare();\n        this.validKey = isValidKey ?? ((v: unknown) => v != null && v != undefined );\n    }\n\n    delete(element: unknown) {\n        if (!this.validKey(element)) return false;\n        return this._delete(element as E) != null;\n    }\n\n    deleteAll(elements: Iterable<unknown>) {\n        for (const element of elements) {\n            this.delete(element);\n        }\n    }\n\n    forEach(f: (element: E, element2: E, set: Set<E>) => void) {\n        const nodes: Iterator<E> = this[Symbol.iterator]();\n        let result: IteratorResult<E>;\n        while (result = nodes.next(), !result.done) {\n            f(result.value, result.value, this);\n        }\n    }\n\n    add(element: E) {\n        const compare = this.splay(element);\n        if (compare != 0) this.addNewRoot(new SplayTreeSetNode(element), compare);\n        return this;\n    }\n\n    addAndReturn(element: E) {\n        const compare = this.splay(element);\n        if (compare != 0) this.addNewRoot(new SplayTreeSetNode(element), compare);\n        return this.root!.key;\n    }\n\n    addAll(elements: Iterable<E>) {\n        for (const element of elements) {\n            this.add(element);\n        }\n    }\n\n    isEmpty() {\n        return this.root == null;\n    }\n\n    isNotEmpty() {\n        return this.root != null;\n    }\n\n    single() {\n        if (this.size == 0) throw \"Bad state: No element\";\n        if (this.size > 1) throw \"Bad state: Too many element\";\n        return this.root!.key;\n    }\n\n    first() {\n        if (this.size == 0) throw \"Bad state: No element\";\n        return this._first()!.key;\n    }\n\n    last() {\n        if (this.size == 0) throw \"Bad state: No element\";\n        return this._last()!.key;\n    }\n\n    lastBefore(element: E) {\n        if (element == null) throw \"Invalid arguments(s)\";\n        if (this.root == null) return null;\n        const comp = this.splay(element);\n        if (comp < 0) return this.root!.key;\n        let node: SplayTreeSetNode<E> | null = this.root!.left;\n        if (node == null) return null;\n        let nodeRight = node.right;\n        while (nodeRight != null) {\n            node = nodeRight;\n            nodeRight = node.right;\n        }\n        return node!.key;\n    }\n\n    firstAfter(element: E) {\n        if (element == null) throw \"Invalid arguments(s)\";\n        if (this.root == null) return null;\n        const comp = this.splay(element);\n        if (comp > 0) return this.root!.key;\n        let node: SplayTreeSetNode<E> | null = this.root!.right;\n        if (node == null) return null;\n        let nodeLeft = node.left;\n        while (nodeLeft != null) {\n            node = nodeLeft;\n            nodeLeft = node.left;\n        }\n        return node!.key;\n    }\n\n    retainAll(elements: Iterable<unknown>) {\n        const retainSet = new SplayTreeSet<E>(this.compare, this.validKey);\n        const modificationCount = this.modificationCount;\n        for (const object of elements) {\n            if (modificationCount != this.modificationCount) {\n                throw \"Concurrent modification during iteration.\";\n            }\n            if (this.validKey(object) && this.splay(object as E) == 0) {\n                retainSet.add(this.root!.key);\n            }\n        }\n        if (retainSet.size != this.size) {\n            this.root = retainSet.root;\n            this.size = retainSet.size;\n            this.modificationCount++;\n        }\n    }\n\n    lookup(object: unknown): E | null {\n        if (!this.validKey(object)) return null;\n        const comp = this.splay(object as E);\n        if (comp != 0) return null;\n        return this.root!.key;\n    }\n\n    intersection(other: Set<unknown>): Set<E> {\n        const result = new SplayTreeSet<E>(this.compare, this.validKey);\n        for (const element of this) {\n            if (other.has(element)) result.add(element);\n        }\n        return result;\n    }\n\n    difference(other: Set<unknown>): Set<E> {\n        const result = new SplayTreeSet<E>(this.compare, this.validKey);\n        for (const element of this) {\n            if (!other.has(element)) result.add(element);\n        }\n        return result;\n    }\n\n    union(other: Set<E>): Set<E> {\n        const u = this.clone();\n        u.addAll(other);\n        return u;\n    }\n\n    protected clone() {\n        const set = new SplayTreeSet<E>(this.compare, this.validKey);\n        set.size = this.size;\n        set.root = this.copyNode<SplayTreeSetNode<E>>(this.root);\n        return set;\n    }\n\n    protected copyNode<Node extends SplayTreeNode<E, Node>>(node: Node | null) {\n        if (node == null) return null;\n        function copyChildren(node: Node, dest: SplayTreeSetNode<E>) {\n            let left: Node | null;\n            let right: Node | null;\n            do {\n                left = node.left;\n                right = node.right;\n                if (left != null) {\n                    const newLeft = new SplayTreeSetNode<E>(left.key);\n                    dest.left = newLeft;\n                    copyChildren(left, newLeft);\n                }\n                if (right != null) {\n                    const newRight = new SplayTreeSetNode<E>(right.key);\n                    dest.right = newRight;\n                    node = right;\n                    dest = newRight;\n                }\n            } while (right != null);\n        }\n\n        const result = new SplayTreeSetNode<E>(node.key);\n        copyChildren(node, result);\n        return result;\n    }\n\n    toSet(): Set<E> {\n        return this.clone();\n    }\n\n    entries(): IterableIterator<[E, E]> {\n        return new SplayTreeSetEntryIterableIterator<E, SplayTreeSetNode<E>>(this.wrap());\n    }\n\n    keys(): IterableIterator<E> {\n        return this[Symbol.iterator]();\n    }\n    \n    values(): IterableIterator<E> {\n        return this[Symbol.iterator]();\n    }\n\n    [Symbol.iterator](): IterableIterator<E> {\n        return new SplayTreeKeyIterableIterator<E, SplayTreeSetNode<E>>(this.wrap());\n    }\n\n    [Symbol.toStringTag] = '[object Set]'\n}\n\ninterface SplayTreeWrapper<K, Node extends SplayTreeNode<K, Node>> {\n    getRoot: () => Node | null;\n    setRoot: (root: Node | null) => void;\n    getSize: () => number;\n    getModificationCount: () => number;\n    getSplayCount: () => number;\n    setSplayCount: (count: number) => void;\n    splay: (key: K) => number;\n    has: (key: unknown) => boolean;\n}\n\ntype SplayTreeMapWrapper<K, V> = SplayTreeWrapper<K, SplayTreeMapNode<K, V>>;\n\nabstract class SplayTreeIterableIterator<K, Node extends SplayTreeNode<K, Node>, T> implements IterableIterator<T> {\n    protected readonly tree: SplayTreeWrapper<K, Node>;\n\n    protected readonly path = new Array<Node>();\n\n    protected modificationCount: number | null = null;\n\n    protected splayCount: number;\n\n    constructor(tree: SplayTreeWrapper<K, Node>) {\n        this.tree = tree;\n        this.splayCount = tree.getSplayCount();\n    }\n\n    [Symbol.iterator](): IterableIterator<T> {\n        return this;\n    }\n\n    next(): IteratorResult<T, null> {\n        if (this.moveNext()) return { done: false, value: this.current()! }\n        return { done: true, value: null }\n    }\n\n    protected current() {\n        if (!this.path.length) return null;\n        const node = this.path[this.path.length - 1];\n        return this.getValue(node);\n    }\n\n    protected rebuildPath(key: K) {\n        this.path.splice(0, this.path.length)\n        this.tree.splay(key);\n        this.path.push(this.tree.getRoot()!);\n        this.splayCount = this.tree.getSplayCount();\n    }\n\n    protected findLeftMostDescendent(node: Node | null) {\n        while (node != null) {\n            this.path.push(node);\n            node = node.left;\n        }\n    }\n\n    protected moveNext() {\n        if (this.modificationCount != this.tree.getModificationCount()) {\n            if (this.modificationCount == null) {\n                this.modificationCount = this.tree.getModificationCount();\n                let node = this.tree.getRoot();\n                while (node != null) {\n                    this.path.push(node);\n                    node = node.left;\n                }\n                return this.path.length > 0;\n            }\n            throw \"Concurrent modification during iteration.\";\n        }\n        if (!this.path.length) return false;\n        if (this.splayCount != this.tree.getSplayCount()) {\n            this.rebuildPath(this.path[this.path.length - 1].key);\n        }\n        let node = this.path[this.path.length - 1];\n        let next = node.right;\n        if (next != null) {\n            while (next != null) {\n                this.path.push(next);\n                next = next.left;\n            }\n            return true;\n        }\n        this.path.pop();\n        while (this.path.length && this.path[this.path.length - 1].right === node) {\n            node = this.path.pop()!;\n        }\n        return this.path.length > 0;\n    }\n\n    protected abstract getValue(node: Node): T\n}\n\nclass SplayTreeKeyIterableIterator<K, Node extends SplayTreeNode<K, Node>> extends SplayTreeIterableIterator<K, Node, K> {\n\n    protected getValue(node: Node) {\n        return node.key;\n    }\n}\n\nclass SplayTreeSetEntryIterableIterator<K, Node extends SplayTreeNode<K, Node>> extends SplayTreeIterableIterator<K, Node, [K, K]> {\n\n    protected getValue(node: Node): [K, K] {\n        return [node.key, node.key];\n    }\n}\n\nclass SplayTreeValueIterableIterator<K, V> extends SplayTreeIterableIterator<K, SplayTreeMapNode<K, V>, V> {\n\n    constructor(map: SplayTreeMapWrapper<K, V>) {\n        super(map);\n    }\n\n    protected getValue(node: SplayTreeMapNode<K, V>) {\n        return node.value;\n    }\n}\n\nclass SplayTreeMapEntryIterableIterator<K, V> extends SplayTreeIterableIterator<K, SplayTreeMapNode<K, V>, [K, V]> {\n\n    constructor(map: SplayTreeMapWrapper<K, V>) {\n        super(map);\n    }\n\n    protected getValue(node: SplayTreeMapNode<K, V>): [K, V] {\n        return [node.key, node.value];\n    }\n\n    replaceValue(value: V) {\n        if (this.modificationCount != this.tree.getModificationCount()) {\n            throw \"Concurrent modification during iteration.\";\n        }\n        if (this.splayCount != this.tree.getSplayCount()) {\n            this.rebuildPath(this.path[this.path.length - 1].key);\n        }\n        const last = this.path.pop()!;\n        const newLast = last.replaceValue(value);\n        if (!this.path.length) {\n            this.tree.setRoot(newLast);\n        } else {\n            const parent = this.path[this.path.length - 1];\n            if (last === parent.left) {\n                parent.left = newLast;\n            } else {\n                parent.right = newLast;\n            }\n        }\n        this.path.push(newLast);\n        const count = this.tree.getSplayCount() + 1;\n        this.tree.setSplayCount(count);\n        this.splayCount = count;\n    }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,IAAM,gBAAN,MAA4D;AAAA,EAC/C;AAAA,EAET,OAAoB;AAAA,EACpB,QAAqB;AAAA,EAErB,YAAY,KAAQ;AAChB,SAAK,MAAM;AAAA,EACf;AACJ;AAEA,IAAM,mBAAN,cAAkC,cAAsC;AAAA,EACpE,YAAY,KAAQ;AAChB,UAAM,GAAG;AAAA,EACb;AACJ;AAEA,IAAM,mBAAN,MAAM,0BAA+B,cAAyC;AAAA,EACjE;AAAA,EAET,YAAY,KAAQ,OAAU;AAC1B,UAAM,GAAG;AACT,SAAK,QAAQ;AAAA,EACjB;AAAA,EAEA,aAAa,OAAU;AACnB,UAAM,OAAO,IAAI,kBAAiB,KAAK,KAAK,KAAK;AACjD,SAAK,OAAO,KAAK;AACjB,SAAK,QAAQ,KAAK;AAClB,WAAO;AAAA,EACX;AACJ;AAEA,IAAe,YAAf,MAAiE;AAAA,EAGtD,OAAO;AAAA,EAEJ,oBAAoB;AAAA,EAEpB,aAAa;AAAA,EAMb,MAAM,KAAQ;AACpB,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,MAAM;AACd,WAAK,QAAQ,KAAK,GAAG;AACrB,aAAO;AAAA,IACX;AAEA,QAAI,QAAqB;AACzB,QAAI,eAA4B;AAChC,QAAI,OAAoB;AACxB,QAAI,cAA2B;AAC/B,QAAI,UAAU;AACd,UAAM,UAAU,KAAK;AACrB,QAAI;AACJ,WAAO,MAAM;AACT,aAAO,QAAQ,QAAQ,KAAK,GAAG;AAC/B,UAAI,OAAO,GAAG;AACV,YAAI,cAAc,QAAQ;AAC1B,YAAI,eAAe,KAAM;AACzB,eAAO,QAAQ,YAAY,KAAK,GAAG;AACnC,YAAI,OAAO,GAAG;AACV,kBAAQ,OAAO,YAAY;AAC3B,sBAAY,QAAQ;AACpB,oBAAU;AACV,wBAAc,QAAQ;AACtB,cAAI,eAAe,KAAM;AAAA,QAC7B;AACA,YAAI,SAAS,MAAM;AACf,yBAAe;AAAA,QACnB,OAAO;AACH,gBAAM,OAAO;AAAA,QACjB;AACA,gBAAQ;AACR,kBAAU;AAAA,MACd,WAAW,OAAO,GAAG;AACjB,YAAI,eAAe,QAAQ;AAC3B,YAAI,gBAAgB,KAAM;AAC1B,eAAO,QAAQ,aAAa,KAAK,GAAG;AACpC,YAAI,OAAO,GAAG;AACV,kBAAQ,QAAQ,aAAa;AAC7B,uBAAa,OAAO;AACpB,oBAAU;AACV,yBAAe,QAAQ;AACvB,cAAI,gBAAgB,KAAM;AAAA,QAC9B;AACA,YAAI,QAAQ,MAAM;AACd,wBAAc;AAAA,QAClB,OAAO;AACH,eAAK,QAAQ;AAAA,QACjB;AACA,eAAO;AACP,kBAAU;AAAA,MACd,OAAO;AACH;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,QAAQ,MAAM;AACd,WAAK,QAAQ,QAAQ;AACrB,cAAQ,OAAO;AAAA,IACnB;AACA,QAAI,SAAS,MAAM;AACf,YAAM,OAAO,QAAQ;AACrB,cAAQ,QAAQ;AAAA,IACpB;AACA,QAAI,KAAK,SAAS,SAAS;AACvB,WAAK,OAAO;AACZ,WAAK;AAAA,IACT;AACA,WAAO;AAAA,EACX;AAAA,EAEU,SAAS,MAAY;AAC3B,QAAI,UAAU;AACd,QAAI,WAAW,QAAQ;AACvB,WAAO,YAAY,MAAM;AACrB,YAAM,OAAO;AACb,cAAQ,OAAO,KAAK;AACpB,WAAK,QAAQ;AACb,gBAAU;AACV,iBAAW,QAAQ;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AAAA,EAEU,SAAS,MAAY;AAC3B,QAAI,UAAU;AACd,QAAI,YAAY,QAAQ;AACxB,WAAO,aAAa,MAAM;AACtB,YAAM,QAAQ;AACd,cAAQ,QAAQ,MAAM;AACtB,YAAM,OAAO;AACb,gBAAU;AACV,kBAAY,QAAQ;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AAAA,EAEU,QAAQ,KAAQ;AACtB,QAAI,KAAK,QAAQ,KAAM,QAAO;AAC9B,UAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,QAAI,QAAQ,EAAG,QAAO;AACtB,QAAI,OAAO,KAAK;AAChB,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,SAAK;AACL,QAAI,QAAQ,MAAM;AACd,WAAK,OAAO,KAAK;AAAA,IACrB,OAAO;AACH,YAAM,QAAQ,KAAK;AACnB,aAAO,KAAK,SAAS,IAAI;AAEzB,WAAK,QAAQ;AACb,WAAK,OAAO;AAAA,IAChB;AACA,SAAK;AACL,WAAO;AAAA,EACX;AAAA,EAEU,WAAW,MAAY,MAAc;AAC3C,SAAK;AACL,SAAK;AACL,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,MAAM;AACd,WAAK,OAAO;AACZ;AAAA,IACJ;AACA,QAAI,OAAO,GAAG;AACV,WAAK,OAAO;AACZ,WAAK,QAAQ,KAAK;AAClB,WAAK,QAAQ;AAAA,IACjB,OAAO;AACH,WAAK,QAAQ;AACb,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IAChB;AACA,SAAK,OAAO;AAAA,EAChB;AAAA,EAEU,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,KAAM,QAAO;AACzB,SAAK,OAAO,KAAK,SAAS,IAAI;AAC9B,WAAO,KAAK;AAAA,EAChB;AAAA,EAEU,QAAQ;AACd,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,KAAM,QAAO;AACzB,SAAK,OAAO,KAAK,SAAS,IAAI;AAC9B,WAAO,KAAK;AAAA,EAChB;AAAA,EAEO,QAAQ;AACX,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK;AAAA,EACT;AAAA,EAEO,IAAI,KAAc;AACrB,WAAO,KAAK,SAAS,GAAG,KAAK,KAAK,MAAM,GAAQ,KAAK;AAAA,EACzD;AAAA,EAEU,iBAAgC;AACtC,WAAO,CAAC,GAAM,MAAS,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,EACpD;AAAA,EAEU,OAAkC;AACxC,WAAO;AAAA,MACH,SAAS,MAAM;AAAE,eAAO,KAAK;AAAA,MAAK;AAAA,MAClC,SAAS,CAAC,SAAS;AAAE,aAAK,OAAO;AAAA,MAAK;AAAA,MACtC,SAAS,MAAM;AAAE,eAAO,KAAK;AAAA,MAAK;AAAA,MAClC,sBAAsB,MAAM;AAAE,eAAO,KAAK;AAAA,MAAkB;AAAA,MAC5D,eAAe,MAAM;AAAE,eAAO,KAAK;AAAA,MAAW;AAAA,MAC9C,eAAe,CAAC,UAAU;AAAE,aAAK,aAAa;AAAA,MAAM;AAAA,MACpD,OAAO,CAAC,QAAQ;AAAE,eAAO,KAAK,MAAM,GAAG;AAAA,MAAE;AAAA,MACzC,KAAK,CAAC,QAAQ;AAAE,eAAO,KAAK,IAAI,GAAG;AAAA,MAAE;AAAA,IACzC;AAAA,EACJ;AACJ;AAEO,IAAM,eAAN,cAAiC,UAA4E;AAAA,EACtG,OAAsC;AAAA,EAEtC;AAAA,EACA;AAAA,EAEV,YAAY,SAAyB,YAAiC;AAClE,UAAM;AACN,SAAK,UAAU,WAAW,KAAK,eAAe;AAC9C,SAAK,WAAW,eAAe,CAAC,MAAe,KAAK,QAAQ,KAAK;AAAA,EACrE;AAAA,EAEA,OAAO,KAAc;AACjB,QAAI,CAAC,KAAK,SAAS,GAAG,EAAG,QAAO;AAChC,WAAO,KAAK,QAAQ,GAAQ,KAAK;AAAA,EACrC;AAAA,EAEA,QAAQ,GAA+C;AACnD,UAAM,QAA0B,IAAI,kCAAwC,KAAK,KAAK,CAAC;AACvF,QAAI;AACJ,WAAO,SAAS,MAAM,KAAK,GAAG,CAAC,OAAO,MAAM;AACxC,QAAE,OAAO,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,IAC5C;AAAA,EACJ;AAAA,EAEA,IAAI,KAA6B;AAC7B,QAAI,CAAC,KAAK,SAAS,GAAG,EAAG,QAAO;AAChC,QAAI,KAAK,QAAQ,MAAM;AACnB,YAAM,OAAO,KAAK,MAAM,GAAQ;AAChC,UAAI,QAAQ,GAAG;AACX,eAAO,KAAK,KAAM;AAAA,MACtB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EAEA,SAAS,OAAgB;AACrB,UAAM,oBAAoB,KAAK;AAC/B,UAAM,QAAQ,CAAC,SAAwC;AACnD,aAAO,QAAQ,MAAM;AACjB,YAAI,KAAK,SAAS,MAAO,QAAO;AAChC,YAAI,qBAAqB,KAAK,YAAY;AACtC,gBAAM;AAAA,QACV;AACA,YAAI,KAAK,SAAS,QAAQ,MAAM,KAAK,KAAK,GAAG;AACzC,iBAAO;AAAA,QACX;AACA,eAAO,KAAK;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AAEA,WAAO,MAAM,KAAK,IAAI;AAAA,EAC1B;AAAA,EAEA,IAAI,KAAQ,OAAU;AAClB,UAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,QAAI,QAAQ,GAAG;AACX,WAAK,OAAO,KAAK,KAAM,aAAa,KAAK;AACzC,WAAK,cAAc;AACnB,aAAO;AAAA,IACX;AACA,SAAK,WAAW,IAAI,iBAAiB,KAAK,KAAK,GAAG,IAAI;AACtD,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,OAAkB;AACrB,UAAM,QAAQ,CAAC,OAAU,QAAW;AAChC,WAAK,IAAI,KAAK,KAAK;AAAA,IACvB,CAAC;AAAA,EACL;AAAA,EAEA,YAAY,KAAQ,UAAmB;AACnC,QAAI,OAAO,KAAK,MAAM,GAAG;AACzB,QAAI,QAAQ,GAAG;AACX,aAAO,KAAK,KAAM;AAAA,IACtB;AACA,UAAM,oBAAoB,KAAK;AAC/B,UAAM,aAAa,KAAK;AACxB,UAAM,QAAQ,SAAS;AACvB,QAAI,qBAAqB,KAAK,mBAAmB;AAC7C,YAAM;AAAA,IACV;AACA,QAAI,cAAc,KAAK,YAAY;AAC/B,aAAO,KAAK,MAAM,GAAG;AAAA,IACzB;AACA,SAAK,WAAW,IAAI,iBAAiB,KAAK,KAAK,GAAG,IAAI;AACtD,WAAO;AAAA,EACX;AAAA,EAEA,UAAU;AACN,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA,EAEA,aAAa;AACT,WAAO,CAAC,KAAK,QAAQ;AAAA,EACzB;AAAA,EAEA,WAAW;AACP,QAAI,KAAK,QAAQ,KAAM,QAAO;AAC9B,WAAO,KAAK,OAAO,EAAG;AAAA,EAC1B;AAAA,EAEA,UAAU;AACN,QAAI,KAAK,QAAQ,KAAM,QAAO;AAC9B,WAAO,KAAK,MAAM,EAAG;AAAA,EACzB;AAAA,EAEA,cAAc,KAAQ;AAClB,QAAI,OAAO,KAAM,OAAM;AACvB,QAAI,KAAK,QAAQ,KAAM,QAAO;AAC9B,UAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,QAAI,OAAO,EAAG,QAAO,KAAK,KAAM;AAChC,QAAI,OAAsC,KAAK,KAAM;AACrD,QAAI,QAAQ,KAAM,QAAO;AACzB,QAAI,YAAY,KAAK;AACrB,WAAO,aAAa,MAAM;AACtB,aAAO;AACP,kBAAY,KAAK;AAAA,IACrB;AACA,WAAO,KAAM;AAAA,EACjB;AAAA,EAEA,cAAc,KAAQ;AAClB,QAAI,OAAO,KAAM,OAAM;AACvB,QAAI,KAAK,QAAQ,KAAM,QAAO;AAC9B,UAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,QAAI,OAAO,EAAG,QAAO,KAAK,KAAM;AAChC,QAAI,OAAsC,KAAK,KAAM;AACrD,QAAI,QAAQ,KAAM,QAAO;AACzB,QAAI,WAAW,KAAK;AACpB,WAAO,YAAY,MAAM;AACrB,aAAO;AACP,iBAAW,KAAK;AAAA,IACpB;AACA,WAAO,KAAM;AAAA,EACjB;AAAA,EAEA,OAAO,KAAQ,QAAyB,UAAoB;AACxD,QAAI,OAAO,KAAK,MAAM,GAAG;AACzB,QAAI,QAAQ,GAAG;AACX,YAAM,oBAAoB,KAAK;AAC/B,YAAM,aAAa,KAAK;AACxB,YAAM,WAAW,OAAO,KAAK,KAAM,KAAK;AACxC,UAAI,qBAAqB,KAAK,mBAAmB;AAC7C,cAAM;AAAA,MACV;AACA,UAAI,cAAc,KAAK,YAAY;AAC/B,aAAK,MAAM,GAAG;AAAA,MAClB;AACA,WAAK,OAAO,KAAK,KAAM,aAAa,QAAQ;AAC5C,WAAK,cAAc;AACnB,aAAO;AAAA,IACX;AACA,QAAI,YAAY,MAAM;AAClB,YAAM,oBAAoB,KAAK;AAC/B,YAAM,aAAa,KAAK;AACxB,YAAM,WAAW,SAAS;AAC1B,UAAI,qBAAqB,KAAK,mBAAmB;AAC7C,cAAM;AAAA,MACV;AACA,UAAI,cAAc,KAAK,YAAY;AAC/B,eAAO,KAAK,MAAM,GAAG;AAAA,MACzB;AACA,WAAK,WAAW,IAAI,iBAAiB,KAAK,QAAQ,GAAG,IAAI;AACzD,aAAO;AAAA,IACX;AACA,UAAM;AAAA,EACV;AAAA,EAEA,UAAU,QAAiC;AACvC,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,KAAM;AAClB,UAAM,WAAW,IAAI,kCAAkC,KAAK,KAAK,CAAC;AAClE,QAAI;AACJ,WAAO,OAAO,SAAS,KAAK,GAAG,CAAC,KAAK,MAAM;AACvC,YAAM,WAAW,OAAO,GAAG,KAAK,KAAK;AACrC,eAAS,aAAa,QAAQ;AAAA,IAClC;AAAA,EACJ;AAAA,EAEA,OAA4B;AACxB,WAAO,IAAI,6BAAwD,KAAK,KAAK,CAAC;AAAA,EAClF;AAAA,EAEA,SAA8B;AAC1B,WAAO,IAAI,+BAAqC,KAAK,KAAK,CAAC;AAAA,EAC/D;AAAA,EAEA,UAAoC;AAChC,WAAO,KAAK,OAAO,QAAQ,EAAE;AAAA,EACjC;AAAA,EAEA,CAAC,OAAO,QAAQ,IAA8B;AAC1C,WAAO,IAAI,kCAAwC,KAAK,KAAK,CAAC;AAAA,EAClE;AAAA,EAEA,CAAC,OAAO,WAAW,IAAI;AAC3B;AAEO,IAAM,eAAN,MAAM,sBAAwB,UAAiE;AAAA,EACxF,OAAmC;AAAA,EAEnC;AAAA,EACA;AAAA,EAEV,YAAY,SAAyB,YAAiC;AAClE,UAAM;AACN,SAAK,UAAU,WAAW,KAAK,eAAe;AAC9C,SAAK,WAAW,eAAe,CAAC,MAAe,KAAK,QAAQ,KAAK;AAAA,EACrE;AAAA,EAEA,OAAO,SAAkB;AACrB,QAAI,CAAC,KAAK,SAAS,OAAO,EAAG,QAAO;AACpC,WAAO,KAAK,QAAQ,OAAY,KAAK;AAAA,EACzC;AAAA,EAEA,UAAU,UAA6B;AACnC,eAAW,WAAW,UAAU;AAC5B,WAAK,OAAO,OAAO;AAAA,IACvB;AAAA,EACJ;AAAA,EAEA,QAAQ,GAAmD;AACvD,UAAM,QAAqB,KAAK,OAAO,QAAQ,EAAE;AACjD,QAAI;AACJ,WAAO,SAAS,MAAM,KAAK,GAAG,CAAC,OAAO,MAAM;AACxC,QAAE,OAAO,OAAO,OAAO,OAAO,IAAI;AAAA,IACtC;AAAA,EACJ;AAAA,EAEA,IAAI,SAAY;AACZ,UAAM,UAAU,KAAK,MAAM,OAAO;AAClC,QAAI,WAAW,EAAG,MAAK,WAAW,IAAI,iBAAiB,OAAO,GAAG,OAAO;AACxE,WAAO;AAAA,EACX;AAAA,EAEA,aAAa,SAAY;AACrB,UAAM,UAAU,KAAK,MAAM,OAAO;AAClC,QAAI,WAAW,EAAG,MAAK,WAAW,IAAI,iBAAiB,OAAO,GAAG,OAAO;AACxE,WAAO,KAAK,KAAM;AAAA,EACtB;AAAA,EAEA,OAAO,UAAuB;AAC1B,eAAW,WAAW,UAAU;AAC5B,WAAK,IAAI,OAAO;AAAA,IACpB;AAAA,EACJ;AAAA,EAEA,UAAU;AACN,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA,EAEA,aAAa;AACT,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA,EAEA,SAAS;AACL,QAAI,KAAK,QAAQ,EAAG,OAAM;AAC1B,QAAI,KAAK,OAAO,EAAG,OAAM;AACzB,WAAO,KAAK,KAAM;AAAA,EACtB;AAAA,EAEA,QAAQ;AACJ,QAAI,KAAK,QAAQ,EAAG,OAAM;AAC1B,WAAO,KAAK,OAAO,EAAG;AAAA,EAC1B;AAAA,EAEA,OAAO;AACH,QAAI,KAAK,QAAQ,EAAG,OAAM;AAC1B,WAAO,KAAK,MAAM,EAAG;AAAA,EACzB;AAAA,EAEA,WAAW,SAAY;AACnB,QAAI,WAAW,KAAM,OAAM;AAC3B,QAAI,KAAK,QAAQ,KAAM,QAAO;AAC9B,UAAM,OAAO,KAAK,MAAM,OAAO;AAC/B,QAAI,OAAO,EAAG,QAAO,KAAK,KAAM;AAChC,QAAI,OAAmC,KAAK,KAAM;AAClD,QAAI,QAAQ,KAAM,QAAO;AACzB,QAAI,YAAY,KAAK;AACrB,WAAO,aAAa,MAAM;AACtB,aAAO;AACP,kBAAY,KAAK;AAAA,IACrB;AACA,WAAO,KAAM;AAAA,EACjB;AAAA,EAEA,WAAW,SAAY;AACnB,QAAI,WAAW,KAAM,OAAM;AAC3B,QAAI,KAAK,QAAQ,KAAM,QAAO;AAC9B,UAAM,OAAO,KAAK,MAAM,OAAO;AAC/B,QAAI,OAAO,EAAG,QAAO,KAAK,KAAM;AAChC,QAAI,OAAmC,KAAK,KAAM;AAClD,QAAI,QAAQ,KAAM,QAAO;AACzB,QAAI,WAAW,KAAK;AACpB,WAAO,YAAY,MAAM;AACrB,aAAO;AACP,iBAAW,KAAK;AAAA,IACpB;AACA,WAAO,KAAM;AAAA,EACjB;AAAA,EAEA,UAAU,UAA6B;AACnC,UAAM,YAAY,IAAI,cAAgB,KAAK,SAAS,KAAK,QAAQ;AACjE,UAAM,oBAAoB,KAAK;AAC/B,eAAW,UAAU,UAAU;AAC3B,UAAI,qBAAqB,KAAK,mBAAmB;AAC7C,cAAM;AAAA,MACV;AACA,UAAI,KAAK,SAAS,MAAM,KAAK,KAAK,MAAM,MAAW,KAAK,GAAG;AACvD,kBAAU,IAAI,KAAK,KAAM,GAAG;AAAA,MAChC;AAAA,IACJ;AACA,QAAI,UAAU,QAAQ,KAAK,MAAM;AAC7B,WAAK,OAAO,UAAU;AACtB,WAAK,OAAO,UAAU;AACtB,WAAK;AAAA,IACT;AAAA,EACJ;AAAA,EAEA,OAAO,QAA2B;AAC9B,QAAI,CAAC,KAAK,SAAS,MAAM,EAAG,QAAO;AACnC,UAAM,OAAO,KAAK,MAAM,MAAW;AACnC,QAAI,QAAQ,EAAG,QAAO;AACtB,WAAO,KAAK,KAAM;AAAA,EACtB;AAAA,EAEA,aAAa,OAA6B;AACtC,UAAM,SAAS,IAAI,cAAgB,KAAK,SAAS,KAAK,QAAQ;AAC9D,eAAW,WAAW,MAAM;AACxB,UAAI,MAAM,IAAI,OAAO,EAAG,QAAO,IAAI,OAAO;AAAA,IAC9C;AACA,WAAO;AAAA,EACX;AAAA,EAEA,WAAW,OAA6B;AACpC,UAAM,SAAS,IAAI,cAAgB,KAAK,SAAS,KAAK,QAAQ;AAC9D,eAAW,WAAW,MAAM;AACxB,UAAI,CAAC,MAAM,IAAI,OAAO,EAAG,QAAO,IAAI,OAAO;AAAA,IAC/C;AACA,WAAO;AAAA,EACX;AAAA,EAEA,MAAM,OAAuB;AACzB,UAAM,IAAI,KAAK,MAAM;AACrB,MAAE,OAAO,KAAK;AACd,WAAO;AAAA,EACX;AAAA,EAEU,QAAQ;AACd,UAAM,MAAM,IAAI,cAAgB,KAAK,SAAS,KAAK,QAAQ;AAC3D,QAAI,OAAO,KAAK;AAChB,QAAI,OAAO,KAAK,SAA8B,KAAK,IAAI;AACvD,WAAO;AAAA,EACX;AAAA,EAEU,SAA8C,MAAmB;AACvE,QAAI,QAAQ,KAAM,QAAO;AACzB,aAAS,aAAaA,OAAY,MAA2B;AACzD,UAAI;AACJ,UAAI;AACJ,SAAG;AACC,eAAOA,MAAK;AACZ,gBAAQA,MAAK;AACb,YAAI,QAAQ,MAAM;AACd,gBAAM,UAAU,IAAI,iBAAoB,KAAK,GAAG;AAChD,eAAK,OAAO;AACZ,uBAAa,MAAM,OAAO;AAAA,QAC9B;AACA,YAAI,SAAS,MAAM;AACf,gBAAM,WAAW,IAAI,iBAAoB,MAAM,GAAG;AAClD,eAAK,QAAQ;AACb,UAAAA,QAAO;AACP,iBAAO;AAAA,QACX;AAAA,MACJ,SAAS,SAAS;AAAA,IACtB;AAEA,UAAM,SAAS,IAAI,iBAAoB,KAAK,GAAG;AAC/C,iBAAa,MAAM,MAAM;AACzB,WAAO;AAAA,EACX;AAAA,EAEA,QAAgB;AACZ,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA,EAEA,UAAoC;AAChC,WAAO,IAAI,kCAA0D,KAAK,KAAK,CAAC;AAAA,EACpF;AAAA,EAEA,OAA4B;AACxB,WAAO,KAAK,OAAO,QAAQ,EAAE;AAAA,EACjC;AAAA,EAEA,SAA8B;AAC1B,WAAO,KAAK,OAAO,QAAQ,EAAE;AAAA,EACjC;AAAA,EAEA,CAAC,OAAO,QAAQ,IAAyB;AACrC,WAAO,IAAI,6BAAqD,KAAK,KAAK,CAAC;AAAA,EAC/E;AAAA,EAEA,CAAC,OAAO,WAAW,IAAI;AAC3B;AAeA,IAAe,4BAAf,MAAmH;AAAA,EAC5F;AAAA,EAEA,OAAO,IAAI,MAAY;AAAA,EAEhC,oBAAmC;AAAA,EAEnC;AAAA,EAEV,YAAY,MAAiC;AACzC,SAAK,OAAO;AACZ,SAAK,aAAa,KAAK,cAAc;AAAA,EACzC;AAAA,EAEA,CAAC,OAAO,QAAQ,IAAyB;AACrC,WAAO;AAAA,EACX;AAAA,EAEA,OAAgC;AAC5B,QAAI,KAAK,SAAS,EAAG,QAAO,EAAE,MAAM,OAAO,OAAO,KAAK,QAAQ,EAAG;AAClE,WAAO,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,EACrC;AAAA,EAEU,UAAU;AAChB,QAAI,CAAC,KAAK,KAAK,OAAQ,QAAO;AAC9B,UAAM,OAAO,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAC3C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC7B;AAAA,EAEU,YAAY,KAAQ;AAC1B,SAAK,KAAK,OAAO,GAAG,KAAK,KAAK,MAAM;AACpC,SAAK,KAAK,MAAM,GAAG;AACnB,SAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,CAAE;AACnC,SAAK,aAAa,KAAK,KAAK,cAAc;AAAA,EAC9C;AAAA,EAEU,uBAAuB,MAAmB;AAChD,WAAO,QAAQ,MAAM;AACjB,WAAK,KAAK,KAAK,IAAI;AACnB,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AAAA,EAEU,WAAW;AACjB,QAAI,KAAK,qBAAqB,KAAK,KAAK,qBAAqB,GAAG;AAC5D,UAAI,KAAK,qBAAqB,MAAM;AAChC,aAAK,oBAAoB,KAAK,KAAK,qBAAqB;AACxD,YAAIA,QAAO,KAAK,KAAK,QAAQ;AAC7B,eAAOA,SAAQ,MAAM;AACjB,eAAK,KAAK,KAAKA,KAAI;AACnB,UAAAA,QAAOA,MAAK;AAAA,QAChB;AACA,eAAO,KAAK,KAAK,SAAS;AAAA,MAC9B;AACA,YAAM;AAAA,IACV;AACA,QAAI,CAAC,KAAK,KAAK,OAAQ,QAAO;AAC9B,QAAI,KAAK,cAAc,KAAK,KAAK,cAAc,GAAG;AAC9C,WAAK,YAAY,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,EAAE,GAAG;AAAA,IACxD;AACA,QAAI,OAAO,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AACzC,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,MAAM;AACd,aAAO,QAAQ,MAAM;AACjB,aAAK,KAAK,KAAK,IAAI;AACnB,eAAO,KAAK;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AACA,SAAK,KAAK,IAAI;AACd,WAAO,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,EAAE,UAAU,MAAM;AACvE,aAAO,KAAK,KAAK,IAAI;AAAA,IACzB;AACA,WAAO,KAAK,KAAK,SAAS;AAAA,EAC9B;AAGJ;AAEA,IAAM,+BAAN,cAAmF,0BAAsC;AAAA,EAE3G,SAAS,MAAY;AAC3B,WAAO,KAAK;AAAA,EAChB;AACJ;AAEA,IAAM,oCAAN,cAAwF,0BAA2C;AAAA,EAErH,SAAS,MAAoB;AACnC,WAAO,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC9B;AACJ;AAEA,IAAM,iCAAN,cAAmD,0BAAwD;AAAA,EAEvG,YAAY,KAAgC;AACxC,UAAM,GAAG;AAAA,EACb;AAAA,EAEU,SAAS,MAA8B;AAC7C,WAAO,KAAK;AAAA,EAChB;AACJ;AAEA,IAAM,oCAAN,cAAsD,0BAA6D;AAAA,EAE/G,YAAY,KAAgC;AACxC,UAAM,GAAG;AAAA,EACb;AAAA,EAEU,SAAS,MAAsC;AACrD,WAAO,CAAC,KAAK,KAAK,KAAK,KAAK;AAAA,EAChC;AAAA,EAEA,aAAa,OAAU;AACnB,QAAI,KAAK,qBAAqB,KAAK,KAAK,qBAAqB,GAAG;AAC5D,YAAM;AAAA,IACV;AACA,QAAI,KAAK,cAAc,KAAK,KAAK,cAAc,GAAG;AAC9C,WAAK,YAAY,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,EAAE,GAAG;AAAA,IACxD;AACA,UAAM,OAAO,KAAK,KAAK,IAAI;AAC3B,UAAM,UAAU,KAAK,aAAa,KAAK;AACvC,QAAI,CAAC,KAAK,KAAK,QAAQ;AACnB,WAAK,KAAK,QAAQ,OAAO;AAAA,IAC7B,OAAO;AACH,YAAM,SAAS,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAC7C,UAAI,SAAS,OAAO,MAAM;AACtB,eAAO,OAAO;AAAA,MAClB,OAAO;AACH,eAAO,QAAQ;AAAA,MACnB;AAAA,IACJ;AACA,SAAK,KAAK,KAAK,OAAO;AACtB,UAAM,QAAQ,KAAK,KAAK,cAAc,IAAI;AAC1C,SAAK,KAAK,cAAc,KAAK;AAC7B,SAAK,aAAa;AAAA,EACtB;AACJ;", "names": ["node"]}
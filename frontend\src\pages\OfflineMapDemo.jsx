import React, { useState, useEffect } from 'react';
import OfflineMap from '../components/map/OfflineMap';

const OfflineMapDemo = () => {
  const [equipmentData, setEquipmentData] = useState([]);
  const [selectedEquipment, setSelectedEquipment] = useState(null);
  const [drawnShapes, setDrawnShapes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Charger les données d'équipements
  useEffect(() => {
    const loadEquipmentData = async () => {
      try {
        const response = await fetch('/data/equipment.geojson');
        if (!response.ok) {
          throw new Error('Impossible de charger les données d\'équipements');
        }
        
        const geojsonData = await response.json();
        
        // Convertir GeoJSON en format attendu par le composant
        const equipment = geojsonData.features.map(feature => ({
          id: feature.properties.id,
          name: feature.properties.name,
          type: feature.properties.type,
          status: feature.properties.status,
          latitude: feature.geometry.coordinates[1],
          longitude: feature.geometry.coordinates[0],
          frequency: feature.properties.frequency,
          description: feature.properties.description,
          operator: feature.properties.operator,
          installation_date: feature.properties.installation_date,
          coverage_radius: feature.properties.coverage_radius,
          classification: feature.properties.classification
        }));
        
        setEquipmentData(equipment);
        setLoading(false);
      } catch (err) {
        console.error('Erreur lors du chargement des équipements:', err);
        setError(err.message);
        setLoading(false);
      }
    };

    loadEquipmentData();
  }, []);

  // Gestionnaire de sélection d'équipement
  const handleEquipmentSelect = (equipment) => {
    setSelectedEquipment(equipment);
    console.log('Équipement sélectionné:', equipment);
  };

  // Gestionnaire de formes dessinées
  const handleDrawComplete = (drawData) => {
    console.log('Forme dessinée:', drawData);
    setDrawnShapes(prev => [...prev, {
      id: Date.now(),
      ...drawData,
      timestamp: new Date().toISOString()
    }]);
  };

  // Statistiques des équipements
  const getEquipmentStats = () => {
    const stats = {
      total: equipmentData.length,
      active: equipmentData.filter(eq => eq.status === 'active').length,
      standby: equipmentData.filter(eq => eq.status === 'standby').length,
      offline: equipmentData.filter(eq => eq.status === 'offline').length
    };

    const byType = equipmentData.reduce((acc, eq) => {
      acc[eq.type] = (acc[eq.type] || 0) + 1;
      return acc;
    }, {});

    return { ...stats, byType };
  };

  const stats = getEquipmentStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900 text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Chargement de la carte offline du Maroc...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900 text-white">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold mb-2">Erreur de chargement</h2>
          <p className="text-gray-300">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-900">
      {/* En-tête */}
      <div className="bg-black bg-opacity-90 text-white p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-blue-400">
              🇲🇦 Carte Offline - Royaume du Maroc
            </h1>
            <p className="text-sm text-gray-300">
              Plateforme C2-EW avec tuiles vectorielles offline • Intégrité territoriale respectée
            </p>
          </div>
          
          <div className="flex space-x-6 text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{stats.total}</div>
              <div className="text-gray-300">Équipements</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{stats.active}</div>
              <div className="text-gray-300">Actifs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">{stats.standby}</div>
              <div className="text-gray-300">Standby</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{drawnShapes.length}</div>
              <div className="text-gray-300">Formes</div>
            </div>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 flex">
        {/* Carte */}
        <div className="flex-1">
          <OfflineMap
            equipmentData={equipmentData}
            selectedEquipment={selectedEquipment}
            onEquipmentSelect={handleEquipmentSelect}
            onDrawComplete={handleDrawComplete}
            enableDrawing={true}
            enableMeasurement={true}
          />
        </div>

        {/* Panneau latéral */}
        <div className="w-80 bg-black bg-opacity-90 text-white border-l border-gray-700 overflow-y-auto">
          {/* Équipement sélectionné */}
          {selectedEquipment && (
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-lg font-bold text-blue-400 mb-2">
                📍 Équipement Sélectionné
              </h3>
              <div className="space-y-2 text-sm">
                <div><strong>Nom:</strong> {selectedEquipment.name}</div>
                <div><strong>Type:</strong> {selectedEquipment.type}</div>
                <div>
                  <strong>Statut:</strong> 
                  <span className={`ml-2 px-2 py-1 rounded text-xs ${
                    selectedEquipment.status === 'active' ? 'bg-green-600' :
                    selectedEquipment.status === 'standby' ? 'bg-yellow-600' : 'bg-red-600'
                  }`}>
                    {selectedEquipment.status.toUpperCase()}
                  </span>
                </div>
                <div><strong>Fréquence:</strong> {selectedEquipment.frequency}</div>
                <div><strong>Opérateur:</strong> {selectedEquipment.operator}</div>
                <div><strong>Portée:</strong> {(selectedEquipment.coverage_radius / 1000).toFixed(1)} km</div>
                <div><strong>Position:</strong> {selectedEquipment.latitude.toFixed(4)}°, {selectedEquipment.longitude.toFixed(4)}°</div>
                {selectedEquipment.description && (
                  <div><strong>Description:</strong> {selectedEquipment.description}</div>
                )}
              </div>
            </div>
          )}

          {/* Statistiques par type */}
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-lg font-bold text-green-400 mb-2">
              📊 Répartition par Type
            </h3>
            <div className="space-y-2 text-sm">
              {Object.entries(stats.byType).map(([type, count]) => (
                <div key={type} className="flex justify-between">
                  <span>{type}:</span>
                  <span className="font-bold">{count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Formes dessinées */}
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-lg font-bold text-orange-400 mb-2">
              📏 Formes Dessinées ({drawnShapes.length})
            </h3>
            <div className="space-y-2 text-sm max-h-40 overflow-y-auto">
              {drawnShapes.map((shape, index) => (
                <div key={shape.id} className="bg-gray-800 p-2 rounded">
                  <div className="font-bold">{shape.properties.type}</div>
                  {shape.properties.distance && (
                    <div>Distance: {(shape.properties.distance / 1000).toFixed(2)} km</div>
                  )}
                  {shape.properties.area && (
                    <div>Aire: {(shape.properties.area / 1000000).toFixed(2)} km²</div>
                  )}
                  {shape.properties.radius && (
                    <div>Rayon: {(shape.properties.radius / 1000).toFixed(2)} km</div>
                  )}
                  <div className="text-xs text-gray-400 mt-1">
                    {new Date(shape.properties.timestamp).toLocaleString('fr-FR')}
                  </div>
                </div>
              ))}
              {drawnShapes.length === 0 && (
                <div className="text-gray-400 text-center py-4">
                  Aucune forme dessinée
                </div>
              )}
            </div>
          </div>

          {/* Instructions */}
          <div className="p-4">
            <h3 className="text-lg font-bold text-purple-400 mb-2">
              ℹ️ Instructions
            </h3>
            <div className="space-y-2 text-xs text-gray-300">
              <div>• Cliquez sur un équipement pour le sélectionner</div>
              <div>• Utilisez les outils de dessin pour tracer des formes</div>
              <div>• Les mesures sont calculées automatiquement</div>
              <div>• La carte fonctionne entièrement hors ligne</div>
              <div>• Respecte l'intégrité territoriale du Maroc</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfflineMapDemo;

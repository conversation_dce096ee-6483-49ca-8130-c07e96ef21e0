# @turf/distance

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## distance

Calculates the distance between two [points][1] in degrees, radians, miles, or kilometers.
This uses the [Haversine formula][2] to account for global curvature.

**Parameters**

-   `from` **[Coord][3]** origin point
-   `to` **[<PERSON>ord][3]** destination point
-   `options` **[Object][4]** Optional parameters (optional, default `{}`)
    -   `options.units` **[string][5]** can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)

**Examples**

```javascript
var from = turf.point([-75.343, 39.984]);
var to = turf.point([-75.534, 39.123]);
var options = {units: 'miles'};

var distance = turf.distance(from, to, options);

//addToMap
var addToMap = [from, to];
from.properties.distance = distance;
to.properties.distance = distance;
```

Returns **[number][6]** distance between the two points

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: http://en.wikipedia.org/wiki/Haversine_formula

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/distance
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```

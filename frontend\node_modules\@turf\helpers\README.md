# @turf/helpers

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## earthRadius

Earth Radius used with the Harvesine formula and approximates using a spherical (non-ellipsoid) Earth.

## factors

Unit of measurement factors using a spherical (non-ellipsoid) earth radius.

## unitsFactors

Units of measurement factors based on 1 meter.

## areaFactors

Area of measurement factors based on 1 square meter.

## feature

Wraps a GeoJSON [Geometry][1] in a GeoJSON [Feature][2].

**Parameters**

-   `geometry` **[Geometry][3]** input geometry
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var geometry = {
  "type": "Point",
  "coordinates": [110, 50]
};

var feature = turf.feature(geometry);

//=feature
```

Returns **[Feature][8]** a GeoJSON Feature

## geometry

Creates a GeoJSON [Geometry][1] from a Geometry string type & coordinates.
For GeometryCollection type use `helpers.geometryCollection`

**Parameters**

-   `type` **[string][7]** Geometry Type
-   `coordinates` **[Array][5]&lt;[number][6]>** Coordinates
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Geometry

**Examples**

```javascript
var type = 'Point';
var coordinates = [110, 50];

var geometry = turf.geometry(type, coordinates);

//=geometry
```

Returns **[Geometry][3]** a GeoJSON Geometry

## point

Creates a [Point][9] [Feature][2] from a Position.

**Parameters**

-   `coordinates` **[Array][5]&lt;[number][6]>** longitude, latitude position (each in decimal degrees)
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var point = turf.point([-75.343, 39.984]);

//=point
```

Returns **[Feature][8]&lt;[Point][10]>** a Point feature

## points

Creates a [Point][9] [FeatureCollection][11] from an Array of Point coordinates.

**Parameters**

-   `coordinates` **[Array][5]&lt;[Array][5]&lt;[number][6]>>** an array of Points
-   `properties` **[Object][4]** Translate these properties to each Feature (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the FeatureCollection
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the FeatureCollection

**Examples**

```javascript
var points = turf.points([
  [-75, 39],
  [-80, 45],
  [-78, 50]
]);

//=points
```

Returns **[FeatureCollection][12]&lt;[Point][10]>** Point Feature

## polygon

Creates a [Polygon][13] [Feature][2] from an Array of LinearRings.

**Parameters**

-   `coordinates` **[Array][5]&lt;[Array][5]&lt;[Array][5]&lt;[number][6]>>>** an array of LinearRings
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var polygon = turf.polygon([[[-5, 52], [-4, 56], [-2, 51], [-7, 54], [-5, 52]]], { name: 'poly1' });

//=polygon
```

Returns **[Feature][8]&lt;[Polygon][14]>** Polygon Feature

## polygons

Creates a [Polygon][13] [FeatureCollection][11] from an Array of Polygon coordinates.

**Parameters**

-   `coordinates` **[Array][5]&lt;[Array][5]&lt;[Array][5]&lt;[Array][5]&lt;[number][6]>>>>** an array of Polygon coordinates
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the FeatureCollection

**Examples**

```javascript
var polygons = turf.polygons([
  [[[-5, 52], [-4, 56], [-2, 51], [-7, 54], [-5, 52]]],
  [[[-15, 42], [-14, 46], [-12, 41], [-17, 44], [-15, 42]]],
]);

//=polygons
```

Returns **[FeatureCollection][12]&lt;[Polygon][14]>** Polygon FeatureCollection

## lineString

Creates a [LineString][15] [Feature][2] from an Array of Positions.

**Parameters**

-   `coordinates` **[Array][5]&lt;[Array][5]&lt;[number][6]>>** an array of Positions
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var linestring1 = turf.lineString([[-24, 63], [-23, 60], [-25, 65], [-20, 69]], {name: 'line 1'});
var linestring2 = turf.lineString([[-14, 43], [-13, 40], [-15, 45], [-10, 49]], {name: 'line 2'});

//=linestring1
//=linestring2
```

Returns **[Feature][8]&lt;[LineString][16]>** LineString Feature

## lineStrings

Creates a [LineString][15] [FeatureCollection][11] from an Array of LineString coordinates.

**Parameters**

-   `coordinates` **[Array][5]&lt;[Array][5]&lt;[number][6]>>** an array of LinearRings
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the FeatureCollection
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the FeatureCollection

**Examples**

```javascript
var linestrings = turf.lineStrings([
  [[-24, 63], [-23, 60], [-25, 65], [-20, 69]],
  [[-14, 43], [-13, 40], [-15, 45], [-10, 49]]
]);

//=linestrings
```

Returns **[FeatureCollection][12]&lt;[LineString][16]>** LineString FeatureCollection

## featureCollection

Takes one or more [Features][2] and creates a [FeatureCollection][11].

**Parameters**

-   `features` **[Array][5]&lt;[Feature][8]>** input features
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var locationA = turf.point([-75.343, 39.984], {name: 'Location A'});
var locationB = turf.point([-75.833, 39.284], {name: 'Location B'});
var locationC = turf.point([-75.534, 39.123], {name: 'Location C'});

var collection = turf.featureCollection([
  locationA,
  locationB,
  locationC
]);

//=collection
```

Returns **[FeatureCollection][12]** FeatureCollection of Features

## multiLineString

Creates a [Feature&lt;MultiLineString>][17] based on a
coordinate array. Properties can be added optionally.

**Parameters**

-   `coordinates` **[Array][5]&lt;[Array][5]&lt;[Array][5]&lt;[number][6]>>>** an array of LineStrings
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var multiLine = turf.multiLineString([[[0,0],[10,10]]]);

//=multiLine
```

-   Throws **[Error][18]** if no coordinates are passed

Returns **[Feature][8]&lt;[MultiLineString][19]>** a MultiLineString feature

## multiPoint

Creates a [Feature&lt;MultiPoint>][20] based on a
coordinate array. Properties can be added optionally.

**Parameters**

-   `coordinates` **[Array][5]&lt;[Array][5]&lt;[number][6]>>** an array of Positions
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var multiPt = turf.multiPoint([[0,0],[10,10]]);

//=multiPt
```

-   Throws **[Error][18]** if no coordinates are passed

Returns **[Feature][8]&lt;[MultiPoint][21]>** a MultiPoint feature

## multiPolygon

Creates a [Feature&lt;MultiPolygon>][22] based on a
coordinate array. Properties can be added optionally.

**Parameters**

-   `coordinates` **[Array][5]&lt;[Array][5]&lt;[Array][5]&lt;[Array][5]&lt;[number][6]>>>>** an array of Polygons
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var multiPoly = turf.multiPolygon([[[[0,0],[0,10],[10,10],[10,0],[0,0]]]]);

//=multiPoly
```

-   Throws **[Error][18]** if no coordinates are passed

Returns **[Feature][8]&lt;[MultiPolygon][23]>** a multipolygon feature

## geometryCollection

Creates a [Feature&lt;GeometryCollection>][24] based on a
coordinate array. Properties can be added optionally.

**Parameters**

-   `geometries` **[Array][5]&lt;[Geometry][3]>** an array of GeoJSON Geometries
-   `properties` **[Object][4]** an Object of key-value pairs to add as properties (optional, default `{}`)
-   `options` **[Object][4]** Optional Parameters (optional, default `{}`)
    -   `options.bbox` **[Array][5]&lt;[number][6]>?** Bounding Box Array [west, south, east, north] associated with the Feature
    -   `options.id` **([string][7] \| [number][6])?** Identifier associated with the Feature

**Examples**

```javascript
var pt = {
    "type": "Point",
      "coordinates": [100, 0]
    };
var line = {
    "type": "LineString",
    "coordinates": [ [101, 0], [102, 1] ]
  };
var collection = turf.geometryCollection([pt, line]);

//=collection
```

Returns **[Feature][8]&lt;[GeometryCollection][25]>** a GeoJSON GeometryCollection Feature

## round

Round number to precision

**Parameters**

-   `num` **[number][6]** Number
-   `precision` **[number][6]** Precision (optional, default `0`)

**Examples**

```javascript
turf.round(120.4321)
//=120

turf.round(120.4321, 2)
//=120.43
```

Returns **[number][6]** rounded number

## radiansToLength

Convert a distance measurement (assuming a spherical Earth) from radians to a more friendly unit.
Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet

**Parameters**

-   `radians` **[number][6]** in radians across the sphere
-   `units` **[string][7]** can be degrees, radians, miles, or kilometers inches, yards, metres, meters, kilometres, kilometers. (optional, default `'kilometers'`)

Returns **[number][6]** distance

## lengthToRadians

Convert a distance measurement (assuming a spherical Earth) from a real-world unit into radians
Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet

**Parameters**

-   `distance` **[number][6]** in real units
-   `units` **[string][7]** can be degrees, radians, miles, or kilometers inches, yards, metres, meters, kilometres, kilometers. (optional, default `'kilometers'`)

Returns **[number][6]** radians

## lengthToDegrees

Convert a distance measurement (assuming a spherical Earth) from a real-world unit into degrees
Valid units: miles, nauticalmiles, inches, yards, meters, metres, centimeters, kilometres, feet

**Parameters**

-   `distance` **[number][6]** in real units
-   `units` **[string][7]** can be degrees, radians, miles, or kilometers inches, yards, metres, meters, kilometres, kilometers. (optional, default `'kilometers'`)

Returns **[number][6]** degrees

## bearingToAzimuth

Converts any bearing angle from the north line direction (positive clockwise)
and returns an angle between 0-360 degrees (positive clockwise), 0 being the north line

**Parameters**

-   `bearing` **[number][6]** angle, between -180 and +180 degrees

Returns **[number][6]** angle between 0 and 360 degrees

## radiansToDegrees

Converts an angle in radians to degrees

**Parameters**

-   `radians` **[number][6]** angle in radians

Returns **[number][6]** degrees between 0 and 360 degrees

## degreesToRadians

Converts an angle in degrees to radians

**Parameters**

-   `degrees` **[number][6]** angle between 0 and 360 degrees

Returns **[number][6]** angle in radians

## convertLength

Converts a length to the requested unit.
Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet

**Parameters**

-   `length` **[number][6]** to be converted
-   `originalUnit` **[string][7]** of the length
-   `finalUnit` **[string][7]** returned unit (optional, default `'kilometers'`)

Returns **[number][6]** the converted length

## convertArea

Converts a area to the requested unit.
Valid units: kilometers, kilometres, meters, metres, centimetres, millimeters, acres, miles, yards, feet, inches, hectares

**Parameters**

-   `area` **[number][6]** to be converted
-   `originalUnit` **[string][7]** of the distance (optional, default `'meters'`)
-   `finalUnit` **[string][7]** returned unit (optional, default `'kilometers'`)

Returns **[number][6]** the converted distance

## isNumber

isNumber

**Parameters**

-   `num` **any** Number to validate

**Examples**

```javascript
turf.isNumber(123)
//=true
turf.isNumber('foo')
//=false
```

Returns **[boolean][26]** true/false

## isObject

isObject

**Parameters**

-   `input` **any** variable to validate

**Examples**

```javascript
turf.isObject({elevation: 10})
//=true
turf.isObject('foo')
//=false
```

Returns **[boolean][26]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[8]: https://tools.ietf.org/html/rfc7946#section-3.2

[9]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[10]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[11]: https://tools.ietf.org/html/rfc7946#section-3.3

[12]: https://tools.ietf.org/html/rfc7946#section-3.3

[13]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[14]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[15]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[16]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[17]: Feature<MultiLineString>

[18]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error

[19]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[20]: Feature<MultiPoint>

[21]: https://tools.ietf.org/html/rfc7946#section-3.1.3

[22]: Feature<MultiPolygon>

[23]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[24]: Feature<GeometryCollection>

[25]: https://tools.ietf.org/html/rfc7946#section-3.1.8

[26]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/helpers
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```

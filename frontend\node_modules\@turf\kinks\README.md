# @turf/kinks

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## kinks

Takes a [linestring][1], [multi-linestring][2], [multi-polygon][3], or [polygon][4] and returns [points][5] at all self-intersections.

**Parameters**

-   `featureIn` **[Feature][6]&lt;([LineString][7] \| [MultiLineString][8] \| [MultiPolygon][9] \| [Polygon][10])>** input feature

**Examples**

```javascript
var poly = turf.polygon([[
  [-12.034835, 8.901183],
  [-12.060413, 8.899826],
  [-12.03638, 8.873199],
  [-12.059383, 8.871418],
  [-12.034835, 8.901183]
]]);

var kinks = turf.kinks(poly);

//addToMap
var addToMap = [poly, kinks]
```

Returns **[FeatureCollection][11]&lt;[Point][12]>** self-intersections

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[6]: https://tools.ietf.org/html/rfc7946#section-3.2

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[9]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[10]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[11]: https://tools.ietf.org/html/rfc7946#section-3.3

[12]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/kinks
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```

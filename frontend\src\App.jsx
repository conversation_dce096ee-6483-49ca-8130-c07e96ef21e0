import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuthStore } from '@/store/authStore';
import { useWebSocket } from '@/hooks/useWebSocket';

// Composants
import Layout from '@/components/layout/Layout';
import LoginForm from '@/components/auth/LoginForm';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

// Pages (à créer si nécessaire)
const Dashboard = React.lazy(() => import('@/pages/Dashboard'));
const Equipment = React.lazy(() => import('@/pages/Equipment'));
const Plugins = React.lazy(() => import('@/pages/Plugins'));
const Settings = React.lazy(() => import('@/pages/Settings'));
const OfflineMapDemo = React.lazy(() => import('@/pages/OfflineMapDemo'));

// Composant de chargement
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen bg-c2-black">
    <motion.div
      className="w-16 h-16 border-4 border-c2-blue border-t-transparent rounded-full"
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    />
  </div>
);

// Composant Suspense wrapper
const SuspenseWrapper = ({ children }) => (
  <React.Suspense fallback={<LoadingSpinner />}>
    {children}
  </React.Suspense>
);

function App() {
  const { isAuthenticated, user, refreshToken } = useAuthStore();
  const { isConnected, connectionStatus } = useWebSocket();

  // Vérifier et rafraîchir le token au démarrage (désactivé en mode dev)
  useEffect(() => {
    // En mode développement, on évite le refresh automatique
    console.log('Mode développement - refresh token désactivé');
  }, [isAuthenticated, refreshToken]);

  // Afficher un indicateur de statut de connexion
  const ConnectionStatus = () => {
    if (!isAuthenticated) return null;

    return (
      <AnimatePresence>
        {connectionStatus === 'connecting' && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-0 left-0 right-0 z-50 bg-c2-blue text-white text-center py-2 text-sm"
          >
            Connexion en cours...
          </motion.div>
        )}
        {connectionStatus === 'reconnecting' && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-0 left-0 right-0 z-50 bg-yellow-600 text-white text-center py-2 text-sm"
          >
            Reconnexion en cours...
          </motion.div>
        )}
        {connectionStatus === 'failed' && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-0 left-0 right-0 z-50 bg-red-600 text-white text-center py-2 text-sm"
          >
            Connexion échouée - Fonctionnalités limitées
          </motion.div>
        )}
      </AnimatePresence>
    );
  };

  return (
    <div className="App">
      <ConnectionStatus />
      
      <Routes>
        {/* Route de connexion */}
        <Route 
          path="/login" 
          element={
            isAuthenticated ? (
              <Navigate to="/" replace />
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="min-h-screen bg-c2-black flex items-center justify-center"
              >
                <LoginForm />
              </motion.div>
            )
          } 
        />

        {/* Redirection de la racine vers le dashboard */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Navigate to="/map" replace />
            </ProtectedRoute>
          }
        />

        {/* Interface principale avec carte et sidebars */}
        <Route
          path="/map"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        />

        {/* Page Dashboard séparée */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <SuspenseWrapper>
                <Dashboard />
              </SuspenseWrapper>
            </ProtectedRoute>
          }
        />

        <Route
          path="/equipment"
          element={
            <ProtectedRoute>
              <SuspenseWrapper>
                <Equipment />
              </SuspenseWrapper>
            </ProtectedRoute>
          }
        />

        <Route
          path="/plugins"
          element={
            <ProtectedRoute>
              <SuspenseWrapper>
                <Plugins />
              </SuspenseWrapper>
            </ProtectedRoute>
          }
        />

        <Route
          path="/settings"
          element={
            <ProtectedRoute>
              <SuspenseWrapper>
                <Settings />
              </SuspenseWrapper>
            </ProtectedRoute>
          }
        />

        {/* Route pour la démo de carte offline */}
        <Route
          path="/offline-map"
          element={
            <SuspenseWrapper>
              <OfflineMapDemo />
            </SuspenseWrapper>
          }
        />

        {/* Redirection par défaut */}
        <Route
          path="/"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* Route 404 */}
        <Route 
          path="*" 
          element={
            <div className="min-h-screen bg-c2-black flex items-center justify-center">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-c2-white mb-4">404</h1>
                <p className="text-c2-gray-100 mb-8">Page non trouvée</p>
                <button
                  onClick={() => window.history.back()}
                  className="c2-button-primary px-6 py-2 rounded"
                >
                  Retour
                </button>
              </div>
            </div>
          } 
        />
      </Routes>
    </div>
  );
}

export default App;

# Script PowerShell pour configurer tileserver-gl pour les tuiles du Maroc
# Respecte l'intégrité territoriale du Royaume du Maroc

param(
    [switch]$Force = $false
)

Write-Host "🇲🇦 Configuration du serveur de tuiles vectorielles - Royaume du Maroc" -ForegroundColor Green
Write-Host "==================================================================" -ForegroundColor Green

# Répertoires
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$TilesDir = Join-Path $ProjectRoot "tiles"
$MBTilesFile = Join-Path $TilesDir "maroc.mbtiles"

Write-Host "📁 Répertoire du projet: $ProjectRoot" -ForegroundColor Blue
Write-Host "📁 Répertoire des tuiles: $TilesDir" -ForegroundColor Blue

# Créer le répertoire des tuiles s'il n'existe pas
if (-not (Test-Path $TilesDir)) {
    New-Item -ItemType Directory -Path $TilesDir -Force | Out-Null
    Write-Host "✅ Répertoire des tuiles créé" -ForegroundColor Green
}

# Vérifier si Node.js est installé
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js détecté: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js n'est pas installé. Veuillez l'installer d'abord." -ForegroundColor Red
    exit 1
}

# Vérifier si npm est installé
try {
    $npmVersion = npm --version
    Write-Host "✅ npm détecté: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm n'est pas installé." -ForegroundColor Red
    exit 1
}

# Installer tileserver-gl globalement
Write-Host "📦 Vérification de tileserver-gl..." -ForegroundColor Yellow
try {
    $tileserverVersion = tileserver-gl --version
    Write-Host "✅ tileserver-gl déjà installé: $tileserverVersion" -ForegroundColor Green
} catch {
    Write-Host "📦 Installation de tileserver-gl..." -ForegroundColor Yellow
    npm install -g tileserver-gl
    Write-Host "✅ tileserver-gl installé avec succès" -ForegroundColor Green
}

# Générer les tuiles du Maroc si elles n'existent pas
if (-not (Test-Path $MBTilesFile) -or $Force) {
    Write-Host "🏗️ Génération des tuiles vectorielles du Maroc..." -ForegroundColor Yellow
    
    # Vérifier si Python est disponible
    $pythonCmd = $null
    try {
        python3 --version | Out-Null
        $pythonCmd = "python3"
    } catch {
        try {
            python --version | Out-Null
            $pythonCmd = "python"
        } catch {
            Write-Host "❌ Python n'est pas installé. Nécessaire pour générer les tuiles." -ForegroundColor Red
            exit 1
        }
    }
    
    $pythonVersion = & $pythonCmd --version
    Write-Host "🐍 Python détecté: $pythonVersion" -ForegroundColor Blue
    
    # Exécuter le générateur de tuiles
    Set-Location $ProjectRoot
    & $pythonCmd "scripts/generate_morocco_mbtiles.py" $MBTilesFile
    
    if (Test-Path $MBTilesFile) {
        $fileSize = [math]::Round((Get-Item $MBTilesFile).Length / 1MB, 2)
        Write-Host "✅ Tuiles générées avec succès: $MBTilesFile" -ForegroundColor Green
        Write-Host "📊 Taille du fichier: $fileSize MB" -ForegroundColor Blue
    } else {
        Write-Host "❌ Échec de la génération des tuiles" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ Fichier de tuiles existant: $MBTilesFile" -ForegroundColor Green
}

# Créer un fichier de configuration pour tileserver-gl
$ConfigFile = Join-Path $TilesDir "config.json"
$configContent = @{
    options = @{
        paths = @{
            root = $TilesDir
            fonts = "fonts"
            sprites = "sprites"
            styles = "styles"
            mbtiles = $TilesDir
        }
        domains = @("localhost:8080")
        formatQuality = @{
            jpeg = 80
            webp = 90
        }
        maxzoom = 18
        maxsize = 2048
        pbfAlias = "pbf"
        serveAllFonts = $false
        serveAllStyles = $false
        tileMargin = 0
    }
    styles = @{
        basic = @{
            style = "basic.json"
            tilejson = @{
                type = "overlay"
                attribution = "© Données géographiques Royaume du Maroc"
            }
        }
    }
    data = @{
        maroc = @{
            mbtiles = "maroc.mbtiles"
        }
    }
}

$configContent | ConvertTo-Json -Depth 10 | Set-Content $ConfigFile -Encoding UTF8
Write-Host "✅ Configuration créée: $ConfigFile" -ForegroundColor Green

# Créer un style de base
$StyleFile = Join-Path $TilesDir "basic.json"
$styleContent = @{
    version = 8
    name = "Royaume du Maroc - Style de base"
    metadata = @{
        "mapbox:autocomposite" = $false
        "mapbox:type" = "template"
        "mapbox:groups" = @{}
    }
    sources = @{
        maroc = @{
            type = "vector"
            url = "mbtiles://maroc"
        }
    }
    sprite = ""
    glyphs = ""
    layers = @(
        @{
            id = "background"
            type = "background"
            paint = @{
                "background-color" = "#f8f8f8"
            }
        },
        @{
            id = "boundaries"
            type = "line"
            source = "maroc"
            "source-layer" = "boundaries"
            paint = @{
                "line-color" = "#d62728"
                "line-width" = 2
                "line-opacity" = 1
            }
        },
        @{
            id = "cities"
            type = "circle"
            source = "maroc"
            "source-layer" = "cities"
            paint = @{
                "circle-color" = "#2196F3"
                "circle-radius" = 4
                "circle-stroke-color" = "#ffffff"
                "circle-stroke-width" = 1
            }
        }
    )
}

$styleContent | ConvertTo-Json -Depth 10 | Set-Content $StyleFile -Encoding UTF8
Write-Host "✅ Style de base créé: $StyleFile" -ForegroundColor Green

# Créer un script de démarrage PowerShell
$StartScript = Join-Path $TilesDir "start_server.ps1"
$startScriptContent = @"
# Script PowerShell pour démarrer le serveur de tuiles
Write-Host "🚀 Démarrage du serveur de tuiles vectorielles du Maroc..." -ForegroundColor Green
Write-Host "🌐 Serveur disponible sur: http://localhost:8080" -ForegroundColor Blue
Write-Host "📍 Style JSON: http://localhost:8080/styles/basic/style.json" -ForegroundColor Blue
Write-Host "🗺️ Tuiles PBF: http://localhost:8080/data/maroc/{z}/{x}/{y}.pbf" -ForegroundColor Blue
Write-Host ""
Write-Host "Appuyez sur Ctrl+C pour arrêter le serveur" -ForegroundColor Yellow
Write-Host ""

Set-Location "$TilesDir"
tileserver-gl --config config.json --port 8080 --verbose
"@

$startScriptContent | Set-Content $StartScript -Encoding UTF8
Write-Host "✅ Script de démarrage créé: $StartScript" -ForegroundColor Green

# Créer un script batch pour Windows
$StartBatch = Join-Path $TilesDir "start_server.bat"
$batchContent = @"
@echo off
echo 🚀 Démarrage du serveur de tuiles vectorielles du Maroc...
echo 🌐 Serveur disponible sur: http://localhost:8080
echo 📍 Style JSON: http://localhost:8080/styles/basic/style.json
echo 🗺️ Tuiles PBF: http://localhost:8080/data/maroc/{z}/{x}/{y}.pbf
echo.
echo Appuyez sur Ctrl+C pour arrêter le serveur
echo.

cd /d "$TilesDir"
tileserver-gl --config config.json --port 8080 --verbose
pause
"@

$batchContent | Set-Content $StartBatch -Encoding UTF8
Write-Host "✅ Script batch créé: $StartBatch" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 Configuration terminée avec succès!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Prochaines étapes:" -ForegroundColor Yellow
Write-Host "1. Démarrer le serveur de tuiles:" -ForegroundColor Blue
Write-Host "   PowerShell: .\tiles\start_server.ps1" -ForegroundColor Yellow
Write-Host "   Batch: .\tiles\start_server.bat" -ForegroundColor Yellow
Write-Host ""
Write-Host "2. Tester l'accès aux tuiles:" -ForegroundColor Blue
Write-Host "   Style JSON: http://localhost:8080/styles/basic/style.json" -ForegroundColor Yellow
Write-Host "   Tuiles PBF: http://localhost:8080/data/maroc/{z}/{x}/{y}.pbf" -ForegroundColor Yellow
Write-Host ""
Write-Host "3. Intégrer dans React:" -ForegroundColor Blue
Write-Host "   Le composant OfflineMap.jsx est configuré pour utiliser ces URLs" -ForegroundColor Yellow
Write-Host ""
Write-Host "✅ Le serveur respecte l'intégrité territoriale du Royaume du Maroc" -ForegroundColor Green

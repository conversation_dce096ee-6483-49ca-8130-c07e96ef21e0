# @turf/truncate

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## truncate

Takes a GeoJSON Feature or FeatureCollection and truncates the precision of the geometry.

**Parameters**

-   `geo<PERSON>son` **[GeoJSON][1]** any GeoJSON Feature, FeatureCollection, Geometry or GeometryCollection.
-   `options` **[Object][2]** Optional parameters (optional, default `{}`)
    -   `options.precision` **[number][3]** coordinate decimal precision (optional, default `6`)
    -   `options.coordinates` **[number][3]** maximum number of coordinates (primarly used to remove z coordinates) (optional, default `3`)
    -   `options.mutate` **[boolean][4]** allows GeoJSON input to be mutated (significant performance increase if true) (optional, default `false`)

**Examples**

```javascript
var point = turf.point([
    70.46923055566859,
    58.11088890802906,
    1508
]);
var options = {precision: 3, coordinates: 2};
var truncated = turf.truncate(point, options);
//=truncated.geometry.coordinates => [70.469, 58.111]

//addToMap
var addToMap = [truncated];
```

Returns **[GeoJSON][1]** layer with truncated geometry

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/truncate
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```

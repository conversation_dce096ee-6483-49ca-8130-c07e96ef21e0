{"name": "geo<PERSON><PERSON>-<PERSON><PERSON>", "version": "3.2.0", "description": "GeoJSON implementation of RBush", "main": "index", "types": "index.d.ts", "files": ["index.js", "index.d.ts"], "scripts": {"pretest": "tsc types.ts", "test": "node test.js --coverage", "bench": "node bench.js", "docs": "documentation readme index.js --section=API"}, "keywords": ["g<PERSON><PERSON><PERSON>", "index", "tree", "spatial", "rbush"], "author": "<PERSON> <@DenisCarriere>", "contributors": ["<PERSON> <@mourner>", "<PERSON> <@DenisCarriere>", "<PERSON> <@jvrousseau>"], "license": "MIT", "devDependencies": {"@turf/bbox-polygon": "*", "@turf/random": "*", "@types/node": "*", "benchmark": "*", "documentation": "*", "load-json-file": "*", "tap": "*", "tape": "*", "typescript": "*", "write-json-file": "*"}, "dependencies": {"@turf/bbox": "*", "@turf/helpers": "6.x", "@turf/meta": "6.x", "@types/geojson": "7946.0.8", "rbush": "^3.0.1"}}
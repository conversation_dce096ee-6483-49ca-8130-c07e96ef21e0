# @turf/distance

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## distance

Calculates the distance between two [points](https://tools.ietf.org/html/rfc7946#section-3.1.2) in degrees, radians,
miles, or kilometers. This uses the
[Haversine formula](http://en.wikipedia.org/wiki/Haversine_formula)
to account for global curvature.

**Parameters**

-   `from` **[Coord](https://tools.ietf.org/html/rfc7946#section-3.1.1)** origin point
-   `to` **[Coord](https://tools.ietf.org/html/rfc7946#section-3.1.1)** destination point
-   `options` **[Object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** Optional parameters (optional, default `{}`)
    -   `options.units` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)

**Examples**

```javascript
var from = turf.point([-75.343, 39.984]);
var to = turf.point([-75.534, 39.123]);
var options = {units: 'miles'};

var distance = turf.distance(from, to, options);

//addToMap
var addToMap = [from, to];
from.properties.distance = distance;
to.properties.distance = distance;
```

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** distance between the two points

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/distance
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```

{"name": "polyclip-ts", "description": "Apply boolean polygon clipping operations (intersection, union, difference, xor) to your Polygons & MultiPolygons.", "version": "0.16.8", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "type": "module", "main": "dist/esm/index.js", "module": "dist/esm/index.js", "jsdelivr": "dist/polyclip-ts.umd.min.js", "unpkg": "dist/polyclip-ts.umd.min.js", "types": "dist/esm/index.d.ts", "exports": {"umd": "./dist/polyclip-ts.umd.min.js", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.cjs", "default": "./dist/cjs/index.cjs"}, "files": ["dist"], "keywords": ["polygon", "clipping", "boolean", "intersection", "union", "difference", "xor", "geometry", "mart<PERSON>z"], "repository": {"type": "git", "url": "https://github.com/luizbarboza/polyclip-ts.git"}, "scripts": {"preversion": "npm run test", "version": "npm run build && git add -A", "postversion": "git push && git push --tags && npm publish", "build": "rollup -c && tsup", "test": "npm run test:typecheck && npm run test:lint && npm run test:jest", "test:lint": "eslint src", "test:typecheck": "tsc --noEmit", "test:jest": "jest", "bench": "node bench/bench.js"}, "devDependencies": {"@babel/preset-env": "^7.19.0", "@babel/preset-typescript": "^7.18.6", "@rollup/plugin-json": "4", "@rollup/plugin-node-resolve": "13", "@rollup/plugin-terser": "^0.4.1", "@rollup/plugin-typescript": "^8.5.0", "@types/node": "^18.8.5", "@typescript-eslint/eslint-plugin": "^5.25.0", "@typescript-eslint/parser": "^5.25.0", "benchmark": "^2.1.4", "eslint": "^8.16.0", "eslint-config-prettier": "^8.5.0", "jest": "^27.5.1", "jsts": "^2.12.1", "martinez-polygon-clipping": "^0.7.1", "tsup": "^8.3.5"}, "dependencies": {"bignumber.js": "^9.1.0", "splaytree-ts": "^1.0.2"}}
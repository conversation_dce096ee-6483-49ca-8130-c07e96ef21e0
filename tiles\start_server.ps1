# Script PowerShell pour démarrer le serveur de tuiles vectorielles du Maroc
Write-Host "🇲🇦 Démarrage du serveur de tuiles vectorielles du Royaume du Maroc" -ForegroundColor Green
Write-Host "=================================================================" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Serveur disponible sur: http://localhost:8080" -ForegroundColor Blue
Write-Host "📍 Style JSON: http://localhost:8080/styles/basic/style.json" -ForegroundColor Blue
Write-Host "🗺️ Tuiles PBF: http://localhost:8080/data/maroc/{z}/{x}/{y}.pbf" -ForegroundColor Blue
Write-Host "📊 Viewer: http://localhost:8080" -ForegroundColor Blue
Write-Host ""
Write-Host "✅ Intégrité territoriale respectée (incluant le Sahara marocain)" -ForegroundColor Green
Write-Host ""
Write-Host "Appuyez sur Ctrl+C pour arrêter le serveur" -ForegroundColor Yellow
Write-Host ""

# Changer vers le répertoire des tuiles
Set-Location $PSScriptRoot

# Démarrer tileserver-gl
tileserver-gl --config config.json --port 8080 --verbose

// leaflet-measure.scss

$color-divider: #ddd;
$color-button: #5e66cc;
$color-lightertext: #999;

$spacing-external: 12px;
$spacing-internal: 10px;

$max-width: 280px;

$button-icon-size: 12px;
$button-icon-spacing: 4px;

@mixin hoverbutton {
  color: $color-button;
  text-decoration: none;
  &:hover {
    opacity: 0.5;
    text-decoration: none;
  }
}

@mixin button ($icon) {
  display: inline;
  width: auto;
  height: auto;
  padding-left: 20px;
  margin-right: $button-icon-spacing;
  line-height: 1em;
  border: 0;
  text-align: left;
  color: $color-button;
  &,
  &:hover {
    background-color: transparent;
  }
  background: {
    image: url('assets/#{ $icon }.png');
    repeat: no-repeat;
    position: 0% 50%;
    size: $button-icon-size $button-icon-size;
  }
  .leaflet-retina & {
    background-image: url('assets/#{ $icon }<EMAIL>');
  }

  @include hoverbutton;
}

.leaflet-control-measure,
.leaflet-measure-resultpopup {
  h3 {
    margin: 0 0 $spacing-external 0;
    padding-bottom: $spacing-internal;
    border-bottom: solid 1px $color-divider;
  }
  p {
    margin: $spacing-internal 0 0 0;
    line-height: 1.5em;
    &:first-child {
      margin-top: 0;
    }
  }
  .tasks {
    margin: $spacing-external 0 0 0;
    padding: $spacing-internal 0 0 0;
    border-top: solid 1px $color-divider;
    text-align: right;

    list-style: none;
    list-style-image: none;

    li {
      display: inline;
      margin: 0 $spacing-internal 0 0;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .coorddivider {
    color: $color-lightertext;
  }
}

.leaflet-control-measure {
  max-width: $max-width;
  background: #fff;

  .leaflet-control-measure-toggle,
  .leaflet-control-measure-toggle:hover {
    background: {
      size: 14px 14px;
      image: url(assets/rulers.png);
    }
    border: 0;
    border-radius: 4px;
    .leaflet-touch & {
      border-radius: 2px;
    }

    // Hide text
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;

    .leaflet-retina & {
      background-image: url(assets/<EMAIL>);
    }

    .leaflet-touch & {
      background-size: 16px 16px;
    }
  }

  // Special styling because start prompt has no content, just header and tasks
  .startprompt {
    h3 {
      margin-bottom: $spacing-internal;
    }
    .tasks {
      margin-top: 0;
      padding-top: 0;
      border-top: 0;
      text-align: left;
    }
  }

  .leaflet-control-measure-interaction {
    padding: $spacing-internal $spacing-external;
  }

  .results {
    .group {
      margin-top: $spacing-internal;
      padding-top: $spacing-internal;
      border-top: dotted 1px lighten($color-divider, 5);
      &:first-child {
        margin-top: 0;
        padding-top: 0;
        border-top: 0;
      }
    }
    .heading {
      margin-right: $spacing-internal * 0.5;
      color: $color-lightertext;
    }
  }

  a.start {
    @include button(start);
  }
  a.cancel {
    @include button(cancel);
  }
  a.finish {
    @include button(check);
  }
}

.leaflet-measure-resultpopup {
  a.zoomto {
    @include button(focus);
  }
  a.deletemarkup {
    @include button(trash);
  }
}

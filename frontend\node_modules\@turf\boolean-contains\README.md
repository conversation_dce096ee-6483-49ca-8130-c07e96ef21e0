# @turf/boolean-contains

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanContains

Boolean-contains returns True if the second geometry is completely contained by the first geometry.
The interiors of both geometries must intersect and, the interior and boundary of the secondary (geometry b)
must not intersect the exterior of the primary (geometry a).
Boolean-contains returns the exact opposite result of the `@turf/boolean-within`.

**Parameters**

-   `feature1` **([Geometry][1] \| [Feature][2]&lt;any>)** GeoJSON Feature or Geometry
-   `feature2` **([Geometry][1] \| [Feature][2]&lt;any>)** GeoJSON Feature or Geometry

**Examples**

```javascript
var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);
var point = turf.point([1, 2]);

turf.booleanContains(line, point);
//=true
```

Returns **[boolean][3]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/boolean-contains
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```


### Diagrams

![esri-contains](diagrams/esri-contains.gif)
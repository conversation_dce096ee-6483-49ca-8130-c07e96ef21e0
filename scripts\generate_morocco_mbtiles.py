#!/usr/bin/env python3
"""
Script pour générer des tuiles vectorielles offline du Royaume du Maroc
Respecte l'intégrité territoriale (incluant le Sahara marocain)
"""

import os
import sys
import json
import sqlite3
import gzip
from pathlib import Path
import requests
from typing import Dict, List, Tuple
import math

# Configuration du territoire marocain
MOROCCO_BOUNDS = {
    'north': 35.9,
    'south': 20.7,
    'west': -17.1,
    'east': -1.0
}

# Niveaux de zoom à générer
ZOOM_LEVELS = range(5, 15)  # Du niveau 5 au 14

# URLs des données géographiques (sources libres respectant l'intégrité territoriale)
DATA_SOURCES = {
    'boundaries': {
        'url': 'https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson',
        'filter': 'Morocco',
        'layer': 'boundaries'
    },
    'cities': {
        'url': 'https://raw.githubusercontent.com/lutangar/cities.json/master/cities.json',
        'filter': 'MA',
        'layer': 'cities'
    }
}

class MoroccoTileGenerator:
    def __init__(self, output_path: str = 'maroc.mbtiles'):
        self.output_path = output_path
        self.db_path = output_path
        self.setup_database()
    
    def setup_database(self):
        """Initialise la base de données MBTiles"""
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
        
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        
        # Créer les tables MBTiles standard
        cursor.execute('''
            CREATE TABLE metadata (
                name TEXT,
                value TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE tiles (
                zoom_level INTEGER,
                tile_column INTEGER,
                tile_row INTEGER,
                tile_data BLOB
            )
        ''')
        
        # Métadonnées du tileset
        metadata = [
            ('name', 'Royaume du Maroc'),
            ('description', 'Tuiles vectorielles offline du Royaume du Maroc avec intégrité territoriale'),
            ('version', '1.0'),
            ('format', 'pbf'),
            ('type', 'overlay'),
            ('minzoom', str(min(ZOOM_LEVELS))),
            ('maxzoom', str(max(ZOOM_LEVELS))),
            ('bounds', f"{MOROCCO_BOUNDS['west']},{MOROCCO_BOUNDS['south']},{MOROCCO_BOUNDS['east']},{MOROCCO_BOUNDS['north']}"),
            ('center', f"{(MOROCCO_BOUNDS['west'] + MOROCCO_BOUNDS['east'])/2},{(MOROCCO_BOUNDS['south'] + MOROCCO_BOUNDS['north'])/2},8"),
            ('attribution', '© Données géographiques Royaume du Maroc')
        ]
        
        cursor.executemany('INSERT INTO metadata (name, value) VALUES (?, ?)', metadata)
        self.conn.commit()
        print(f"✅ Base de données MBTiles initialisée: {self.db_path}")
    
    def deg2num(self, lat_deg: float, lon_deg: float, zoom: int) -> Tuple[int, int]:
        """Convertit les coordonnées géographiques en numéros de tuiles"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x: int, y: int, zoom: int) -> Tuple[float, float]:
        """Convertit les numéros de tuiles en coordonnées géographiques"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def create_simple_morocco_geojson(self) -> Dict:
        """Crée un GeoJSON simple du Maroc avec les frontières correctes"""
        # Coordonnées simplifiées du Royaume du Maroc (incluant le Sahara)
        morocco_coordinates = [[
            [-17.1, 35.9],  # Nord-Ouest (Tanger)
            [-1.0, 35.9],   # Nord-Est
            [-1.0, 27.7],   # Est (frontière algérienne)
            [-8.7, 27.7],   # Frontière est du Sahara
            [-8.7, 20.7],   # Sud-Est du Sahara
            [-17.1, 20.7],  # Sud-Ouest du Sahara
            [-17.1, 35.9]   # Retour au point de départ
        ]]
        
        # Villes principales du Maroc
        cities = [
            {"name": "Rabat", "coords": [-6.8498, 33.9716], "type": "capital"},
            {"name": "Casablanca", "coords": [-7.5898, 33.5731], "type": "major"},
            {"name": "Marrakech", "coords": [-7.9811, 31.6295], "type": "major"},
            {"name": "Fès", "coords": [-5.0003, 34.0181], "type": "major"},
            {"name": "Tanger", "coords": [-5.8008, 35.7595], "type": "major"},
            {"name": "Agadir", "coords": [-9.5981, 30.4278], "type": "major"},
            {"name": "Laâyoune", "coords": [-13.2033, 27.1253], "type": "sahara"},
            {"name": "Dakhla", "coords": [-15.9582, 23.7185], "type": "sahara"}
        ]
        
        features = []
        
        # Ajouter les frontières du Maroc
        features.append({
            "type": "Feature",
            "properties": {
                "name": "Royaume du Maroc",
                "name_ar": "المملكة المغربية",
                "type": "country",
                "admin_level": "2"
            },
            "geometry": {
                "type": "Polygon",
                "coordinates": morocco_coordinates
            }
        })
        
        # Ajouter les villes
        for city in cities:
            features.append({
                "type": "Feature",
                "properties": {
                    "name": city["name"],
                    "type": "city",
                    "city_type": city["type"]
                },
                "geometry": {
                    "type": "Point",
                    "coordinates": city["coords"]
                }
            })
        
        return {
            "type": "FeatureCollection",
            "features": features
        }
    
    def create_vector_tile(self, x: int, y: int, z: int) -> bytes:
        """Crée une tuile vectorielle au format PBF pour les coordonnées données"""
        # Calculer les limites de la tuile
        lat_north, lon_west = self.num2deg(x, y, z)
        lat_south, lon_east = self.num2deg(x + 1, y + 1, z)

        # Pour cette démo, créer toujours une tuile avec du contenu basique
        # En production, vous filteriez selon les données géographiques réelles
        simple_pbf = self.create_simple_pbf(x, y, z)
        return gzip.compress(simple_pbf) if simple_pbf else b''

    def create_simple_pbf(self, x: int, y: int, z: int) -> bytes:
        """Crée un PBF simple mais fonctionnel pour la démo"""
        # Créer un PBF basique avec des données géométriques simples
        # Format PBF minimal avec une couche "morocco"

        # En-tête PBF basique
        pbf_data = bytearray()

        # Layer name (tag 1, wire type 2 = length-delimited)
        layer_name = b"morocco"
        pbf_data.extend([0x0A])  # Tag 1, wire type 2
        pbf_data.extend([len(layer_name)])  # Length
        pbf_data.extend(layer_name)  # Data

        # Version (tag 15, wire type 0 = varint)
        pbf_data.extend([0x78, 0x02])  # Tag 15, value 2

        # Extent (tag 5, wire type 0 = varint)
        pbf_data.extend([0x28])  # Tag 5
        pbf_data.extend([0x00, 0x10])  # Extent 4096

        # Toujours ajouter une feature basique pour la démo
        # Feature (tag 2, wire type 2 = length-delimited)
        feature_data = bytearray()

        # ID (tag 1, wire type 0 = varint)
        feature_data.extend([0x08, 0x01])  # ID = 1

        # Type (tag 3, wire type 0 = varint) - 1 = Point
        feature_data.extend([0x18, 0x01])  # Type = Point

        # Geometry (tag 4, wire type 2 = length-delimited)
        geometry = bytearray([0x09, 0x00, 0x02])  # MoveTo command + coordinates
        feature_data.extend([0x22])  # Tag 4
        feature_data.extend([len(geometry)])
        feature_data.extend(geometry)

        # Ajouter la feature à la couche
        pbf_data.extend([0x12])  # Tag 2
        pbf_data.extend([len(feature_data)])
        pbf_data.extend(feature_data)

        return bytes(pbf_data)
    
    def generate_tiles(self):
        """Génère toutes les tuiles pour les niveaux de zoom spécifiés"""
        print(f"🚀 Génération des tuiles vectorielles du Maroc...")
        print(f"📍 Limites: {MOROCCO_BOUNDS}")
        print(f"🔍 Niveaux de zoom: {min(ZOOM_LEVELS)}-{max(ZOOM_LEVELS)}")

        total_tiles = 0
        cursor = self.conn.cursor()

        for zoom in ZOOM_LEVELS:
            print(f"\n📊 Niveau de zoom {zoom}...")

            # Calculer les limites des tuiles pour ce niveau de zoom
            # Corriger l'ordre des coordonnées
            x_min, y_max = self.deg2num(MOROCCO_BOUNDS['south'], MOROCCO_BOUNDS['west'], zoom)
            x_max, y_min = self.deg2num(MOROCCO_BOUNDS['north'], MOROCCO_BOUNDS['east'], zoom)

            # S'assurer que les limites sont correctes
            if x_min > x_max:
                x_min, x_max = x_max, x_min
            if y_min > y_max:
                y_min, y_max = y_max, y_min

            print(f"   🔍 Tuiles X: {x_min} à {x_max}, Y: {y_min} à {y_max}")

            zoom_tiles = 0
            for x in range(x_min, x_max + 1):
                for y in range(y_min, y_max + 1):
                    # Toujours créer une tuile pour cette zone (Maroc)
                    tile_data = self.create_vector_tile(x, y, zoom)
                    if tile_data:  # Seulement si la tuile contient des données
                        # MBTiles utilise le schéma TMS (y inversé)
                        tms_y = (2 ** zoom - 1) - y
                        cursor.execute(
                            'INSERT INTO tiles (zoom_level, tile_column, tile_row, tile_data) VALUES (?, ?, ?, ?)',
                            (zoom, x, tms_y, tile_data)
                        )
                        zoom_tiles += 1

            total_tiles += zoom_tiles
            print(f"   ✅ {zoom_tiles} tuiles générées")

        self.conn.commit()
        print(f"\n🎉 Génération terminée!")
        print(f"📦 Total: {total_tiles} tuiles dans {self.output_path}")
        print(f"💾 Taille du fichier: {os.path.getsize(self.output_path) / 1024 / 1024:.2f} MB")
    
    def close(self):
        """Ferme la connexion à la base de données"""
        if hasattr(self, 'conn'):
            self.conn.close()

def main():
    """Fonction principale"""
    print("🇲🇦 Générateur de tuiles vectorielles du Royaume du Maroc")
    print("=" * 60)
    
    output_file = sys.argv[1] if len(sys.argv) > 1 else 'maroc.mbtiles'
    
    generator = MoroccoTileGenerator(output_file)
    
    try:
        generator.generate_tiles()
        print(f"\n✅ Fichier MBTiles créé avec succès: {output_file}")
        print("\n📋 Prochaines étapes:")
        print("1. Installer tileserver-gl: npm install -g tileserver-gl")
        print(f"2. Lancer le serveur: tileserver-gl {output_file}")
        print("3. Accéder aux tuiles: http://localhost:8080")
        
    except Exception as e:
        print(f"❌ Erreur lors de la génération: {e}")
        sys.exit(1)
    
    finally:
        generator.close()

if __name__ == "__main__":
    main()

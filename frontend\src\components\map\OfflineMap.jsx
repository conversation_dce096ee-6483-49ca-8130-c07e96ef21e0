import React, { useEffect, useRef, useState, useCallback } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet-draw';

// Import des plugins nécessaires pour les tuiles vectorielles et mesures
let VectorGrid;
let GeomanPlugin;
try {
  VectorGrid = require('leaflet.vectorgrid');
} catch (e) {
  console.warn('leaflet.vectorgrid not available, using fallback');
}

try {
  GeomanPlugin = require('@geoman-io/leaflet-geoman-free');
  require('@geoman-io/leaflet-geoman-free/dist/leaflet-geoman.css');
} catch (e) {
  console.warn('leaflet-geoman not available, using leaflet-draw only');
}

// Configuration du Royaume du Maroc (intégrité territoriale complète)
const MOROCCO_CONFIG = {
  name: '<PERSON>au<PERSON> du Maroc',
  // Coordonnées incluant le Sahara marocain
  bounds: [
    [35.9, -17.1], // Nord-Ouest (Tanger)
    [20.7, -1.0]   // Sud-Est (frontière avec la Mauritanie)
  ],
  center: [31.7917, -7.0926], // Rabat (capitale)
  zoom: 6,
  minZoom: 5,
  maxZoom: 18
};

// Configuration des couches d'équipements C2-EW
const EQUIPMENT_LAYERS = {
  COMINT: {
    name: 'COMINT',
    color: '#2196F3',
    icon: '📡',
    zIndex: 1000,
    description: 'Intelligence Communications'
  },
  ELINT: {
    name: 'ELINT',
    color: '#FF9800',
    icon: '📊',
    zIndex: 1001,
    description: 'Intelligence Électronique'
  },
  ANTI_DRONE: {
    name: 'Anti-Drone',
    color: '#F44336',
    icon: '🛡️',
    zIndex: 1002,
    description: 'Contre-mesures Drones'
  },
  JAMMER: {
    name: 'Brouilleur COM',
    color: '#9C27B0',
    icon: '📵',
    zIndex: 1003,
    description: 'Brouillage Communications'
  },
  SENSOR: {
    name: 'Capteur',
    color: '#4CAF50',
    icon: '👁️',
    zIndex: 1004,
    description: 'Capteurs Divers'
  }
};

const OfflineMap = ({
  equipmentData = [],
  selectedEquipment = null,
  onEquipmentSelect = () => {},
  onDrawComplete = () => {},
  enableDrawing = true,
  enableMeasurement = true
}) => {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const layerGroupsRef = useRef({});
  const drawControlRef = useRef(null);
  const drawnItemsRef = useRef(null);
  const vectorTileLayerRef = useRef(null);

  const [isMapReady, setIsMapReady] = useState(false);
  const [tileServerStatus, setTileServerStatus] = useState('checking');

  // Vérifier la disponibilité du serveur de tuiles
  const checkTileServer = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:8080/styles/basic/style.json');
      if (response.ok) {
        setTileServerStatus('available');
        return true;
      }
    } catch (error) {
      console.warn('Tile server not available, using fallback');
    }
    setTileServerStatus('unavailable');
    return false;
  }, []);

  // Initialiser la carte
  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // Créer la carte avec la configuration du Maroc
    const map = L.map(mapRef.current, {
      center: MOROCCO_CONFIG.center,
      zoom: MOROCCO_CONFIG.zoom,
      minZoom: MOROCCO_CONFIG.minZoom,
      maxZoom: MOROCCO_CONFIG.maxZoom,
      zoomControl: true,
      attributionControl: false
    });

    // Définir les limites de la carte au territoire marocain
    map.setMaxBounds(MOROCCO_CONFIG.bounds);

    mapInstanceRef.current = map;
    setIsMapReady(true);

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);

  // Configurer les couches de tuiles vectorielles
  useEffect(() => {
    if (!isMapReady || !mapInstanceRef.current) return;

    const map = mapInstanceRef.current;

    checkTileServer().then(serverAvailable => {
      if (serverAvailable && VectorGrid) {
        // Utiliser les tuiles vectorielles du Maroc
        const vectorTileLayer = VectorGrid.protobuf('http://localhost:8080/data/maroc/{z}/{x}/{y}.pbf', {
          vectorTileLayerStyles: {
            // Style pour la couche "morocco" générée par notre script
            'morocco': {
              fill: true,
              fillColor: '#e8f4f8',
              fillOpacity: 0.6,
              stroke: true,
              color: '#d62728',
              weight: 2,
              opacity: 1
            }
          },
          attribution: '© Données géographiques Royaume du Maroc - Intégrité territoriale',
          maxZoom: MOROCCO_CONFIG.maxZoom,
          interactive: true,
          getFeatureId: function(f) {
            return f.properties.id || f.id;
          }
        });

        // Événements sur les tuiles vectorielles
        vectorTileLayer.on('click', function(e) {
          if (e.layer && e.layer.properties) {
            console.log('Clicked on vector feature:', e.layer.properties);
          }
        });

        vectorTileLayer.addTo(map);
        vectorTileLayerRef.current = vectorTileLayer;

        console.log('✅ Tuiles vectorielles du Maroc chargées');
      } else {
        // Fallback vers une couche de base simple avec limites du Maroc
        const fallbackLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors',
          maxZoom: MOROCCO_CONFIG.maxZoom,
          bounds: MOROCCO_CONFIG.bounds
        });

        fallbackLayer.addTo(map);
        console.warn('⚠️ Using fallback tile layer - vector tiles not available');

        // Ajouter un contour du Maroc en fallback
        const moroccoOutline = L.rectangle(MOROCCO_CONFIG.bounds, {
          color: '#d62728',
          weight: 3,
          fillOpacity: 0.1,
          fillColor: '#e8f4f8'
        }).addTo(map);

        moroccoOutline.bindPopup('Royaume du Maroc<br>Mode fallback - Serveur de tuiles non disponible');
      }
    });
  }, [isMapReady, checkTileServer]);

  // Initialiser les groupes de couches d'équipements
  useEffect(() => {
    if (!isMapReady || !mapInstanceRef.current) return;

    const map = mapInstanceRef.current;

    // Créer les groupes de couches pour chaque type d'équipement
    Object.keys(EQUIPMENT_LAYERS).forEach(type => {
      layerGroupsRef.current[type] = L.layerGroup().addTo(map);
    });

    // Créer le groupe pour les éléments dessinés
    drawnItemsRef.current = new L.FeatureGroup();
    map.addLayer(drawnItemsRef.current);

    // Contrôle des couches d'équipements
    const overlayLayers = {};
    Object.entries(EQUIPMENT_LAYERS).forEach(([type, config]) => {
      overlayLayers[`${config.icon} ${config.name}`] = layerGroupsRef.current[type];
    });
    overlayLayers['Éléments dessinés'] = drawnItemsRef.current;

    L.control.layers({}, overlayLayers, { position: 'topright' }).addTo(map);
  }, [isMapReady]);

  // Configurer les outils de dessin et mesure
  useEffect(() => {
    if (!isMapReady || !mapInstanceRef.current || !enableDrawing) return;

    const map = mapInstanceRef.current;

    // Utiliser Geoman si disponible, sinon Leaflet Draw
    if (GeomanPlugin) {
      // Configuration Geoman (plus avancé)
      map.pm.addControls({
        position: 'topleft',
        drawCircle: true,
        drawMarker: true,
        drawPolygon: true,
        drawPolyline: true,
        drawRectangle: true,
        editMode: true,
        dragMode: true,
        cutPolygon: true,
        removalMode: true,
        rotateMode: false
      });

      // Styles par défaut pour Geoman
      map.pm.setGlobalOptions({
        pathOptions: {
          color: '#2196F3',
          weight: 3,
          fillOpacity: 0.3
        }
      });

      // Événements Geoman
      map.on('pm:create', (e) => {
        const layer = e.layer;
        const shape = e.shape;

        // Calculer les propriétés géométriques
        let properties = {
          type: shape,
          timestamp: new Date().toISOString(),
          tool: 'geoman'
        };

        // Calculs spécifiques selon le type
        if (shape === 'Circle') {
          const radius = layer.getRadius();
          const area = Math.PI * radius * radius;
          properties.radius = Math.round(radius);
          properties.area = Math.round(area);
          properties.perimeter = Math.round(2 * Math.PI * radius);
        } else if (shape === 'Polygon' || shape === 'Rectangle') {
          const latlngs = layer.getLatLngs()[0];
          let area = 0;
          let perimeter = 0;

          for (let i = 0; i < latlngs.length; i++) {
            const j = (i + 1) % latlngs.length;
            area += latlngs[i].lat * latlngs[j].lng;
            area -= latlngs[j].lat * latlngs[i].lng;
            perimeter += latlngs[i].distanceTo(latlngs[j]);
          }

          properties.area = Math.abs(area * 111320 * 111320 / 2);
          properties.perimeter = Math.round(perimeter);
        } else if (shape === 'Line') {
          const latlngs = layer.getLatLngs();
          let distance = 0;
          for (let i = 0; i < latlngs.length - 1; i++) {
            distance += latlngs[i].distanceTo(latlngs[i + 1]);
          }
          properties.distance = Math.round(distance);
        }

        // Ajouter un popup avec les mesures
        const popupContent = createMeasurementPopup(properties);
        layer.bindPopup(popupContent);

        // Callback
        onDrawComplete({
          layer: layer,
          geoJSON: layer.toGeoJSON(),
          properties: properties
        });
      });

    } else {
      // Fallback vers Leaflet Draw
      const drawControl = new L.Control.Draw({
        position: 'topleft',
        draw: {
          polygon: {
            allowIntersection: false,
            drawError: {
              color: '#e1e100',
              message: '<strong>Erreur:</strong> Les lignes ne peuvent pas se croiser!'
            },
            shapeOptions: {
              color: '#2196F3',
              weight: 2,
              fillOpacity: 0.2
            }
          },
          circle: {
            shapeOptions: {
              color: '#FF9800',
              weight: 2,
              fillOpacity: 0.2
            }
          },
          rectangle: {
            shapeOptions: {
              color: '#4CAF50',
              weight: 2,
              fillOpacity: 0.2
            }
          },
          polyline: {
            shapeOptions: {
              color: '#F44336',
              weight: 3
            }
          },
          marker: {
            icon: L.divIcon({
              html: '📍',
              className: 'custom-draw-marker',
              iconSize: [20, 20],
              iconAnchor: [10, 10]
            })
          },
          circlemarker: false
        },
        edit: {
          featureGroup: drawnItemsRef.current,
          remove: true
        }
      });

      map.addControl(drawControl);
      drawControlRef.current = drawControl;

      // Événements Leaflet Draw
      map.on(L.Draw.Event.CREATED, (e) => {
        const layer = e.layer;
        drawnItemsRef.current.addLayer(layer);

        // Calculer les propriétés géométriques
        let properties = {
          type: e.layerType,
          timestamp: new Date().toISOString(),
          tool: 'leaflet-draw'
        };

        if (e.layerType === 'circle') {
          const radius = layer.getRadius();
          const area = Math.PI * radius * radius;
          properties.radius = Math.round(radius);
          properties.area = Math.round(area);
        } else if (e.layerType === 'polygon' || e.layerType === 'rectangle') {
          const latlngs = layer.getLatLngs()[0];
          let area = 0;
          for (let i = 0; i < latlngs.length; i++) {
            const j = (i + 1) % latlngs.length;
            area += latlngs[i].lat * latlngs[j].lng;
            area -= latlngs[j].lat * latlngs[i].lng;
          }
          properties.area = Math.abs(area * 111320 * 111320 / 2);
        } else if (e.layerType === 'polyline') {
          const latlngs = layer.getLatLngs();
          let distance = 0;
          for (let i = 0; i < latlngs.length - 1; i++) {
            distance += latlngs[i].distanceTo(latlngs[i + 1]);
          }
          properties.distance = Math.round(distance);
        }

        // Ajouter un popup avec les mesures
        const popupContent = createMeasurementPopup(properties);
        layer.bindPopup(popupContent);

        // Callback
        onDrawComplete({
          layer: layer,
          geoJSON: layer.toGeoJSON(),
          properties: properties
        });
      });
    }

    return () => {
      if (GeomanPlugin && map.pm) {
        map.pm.removeControls();
      } else if (drawControlRef.current && mapInstanceRef.current) {
        mapInstanceRef.current.removeControl(drawControlRef.current);
      }
    };
  }, [isMapReady, enableDrawing, onDrawComplete]);

  // Fonction utilitaire pour créer le contenu des popups de mesure
  const createMeasurementPopup = (properties) => {
    let content = `<div style="font-family: 'Segoe UI', sans-serif; min-width: 200px;">`;
    content += `<h4 style="margin: 0 0 10px 0; color: #2196F3;">📏 Mesures</h4>`;
    content += `<p><strong>Type:</strong> ${properties.type}</p>`;

    if (properties.distance) {
      content += `<p><strong>Distance:</strong> ${(properties.distance / 1000).toFixed(2)} km</p>`;
    }

    if (properties.area) {
      const areaKm2 = properties.area / 1000000;
      content += `<p><strong>Aire:</strong> ${areaKm2.toFixed(2)} km²</p>`;
    }

    if (properties.radius) {
      content += `<p><strong>Rayon:</strong> ${(properties.radius / 1000).toFixed(2)} km</p>`;
    }

    if (properties.perimeter) {
      content += `<p><strong>Périmètre:</strong> ${(properties.perimeter / 1000).toFixed(2)} km</p>`;
    }

    content += `<p style="font-size: 11px; color: #666; margin-top: 10px;">`;
    content += `Créé le ${new Date(properties.timestamp).toLocaleString('fr-FR')}`;
    content += `</p></div>`;

    return content;
  };

  // Mettre à jour les équipements sur la carte
  useEffect(() => {
    if (!isMapReady || !mapInstanceRef.current || !equipmentData.length) return;

    // Nettoyer les marqueurs existants
    Object.values(layerGroupsRef.current).forEach(group => {
      group.clearLayers();
    });

    // Ajouter les nouveaux marqueurs
    equipmentData.forEach(eq => {
      if (!eq.latitude || !eq.longitude) return;

      const layerConfig = EQUIPMENT_LAYERS[eq.type?.toUpperCase()];
      if (!layerConfig) return;

      // Créer l'icône personnalisée
      const customIcon = L.divIcon({
        html: `
          <div style="
            background-color: ${layerConfig.color};
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.4);
            font-size: 16px;
            cursor: pointer;
          ">
            ${layerConfig.icon}
          </div>
        `,
        className: 'custom-equipment-marker',
        iconSize: [32, 32],
        iconAnchor: [16, 16]
      });

      // Créer le marqueur
      const marker = L.marker([eq.latitude, eq.longitude], {
        icon: customIcon,
        zIndexOffset: layerConfig.zIndex
      });

      // Popup avec informations détaillées
      const popupContent = `
        <div style="color: #333; min-width: 220px; font-family: 'Segoe UI', sans-serif;">
          <h3 style="margin: 0 0 12px 0; color: ${layerConfig.color}; font-size: 16px;">
            ${layerConfig.icon} ${eq.name || 'Équipement'}
          </h3>
          <div style="margin-bottom: 8px;">
            <strong>Type:</strong> ${layerConfig.name}
            <br><small style="color: #666;">${layerConfig.description}</small>
          </div>
          <div style="margin-bottom: 8px;">
            <strong>Statut:</strong>
            <span style="color: ${eq.status === 'active' ? '#4CAF50' : eq.status === 'standby' ? '#FF9800' : '#F44336'};">
              ${eq.status === 'active' ? '🟢 Actif' : eq.status === 'standby' ? '🟡 Standby' : '🔴 Hors ligne'}
            </span>
          </div>
          <div style="margin-bottom: 8px;">
            <strong>Position:</strong> ${eq.latitude.toFixed(4)}°, ${eq.longitude.toFixed(4)}°
          </div>
          ${eq.description ? `<div style="margin-bottom: 8px;"><strong>Description:</strong><br>${eq.description}</div>` : ''}
          ${eq.frequency ? `<div><strong>Fréquence:</strong> ${eq.frequency}</div>` : ''}
        </div>
      `;

      marker.bindPopup(popupContent, {
        maxWidth: 300,
        className: 'custom-popup'
      });

      // Événements
      marker.on('click', () => {
        onEquipmentSelect(eq);
      });

      // Ajouter au groupe de couche approprié
      layerGroupsRef.current[eq.type.toUpperCase()].addLayer(marker);
    });
  }, [isMapReady, equipmentData, onEquipmentSelect]);

  // Centrer sur l'équipement sélectionné
  useEffect(() => {
    if (!mapInstanceRef.current || !selectedEquipment) return;

    if (selectedEquipment.latitude && selectedEquipment.longitude) {
      mapInstanceRef.current.setView(
        [selectedEquipment.latitude, selectedEquipment.longitude],
        14,
        { animate: true, duration: 1 }
      );
    }
  }, [selectedEquipment]);

  return (
    <div className="relative w-full h-full bg-gray-900">
      <div
        ref={mapRef}
        className="w-full h-full"
        style={{ minHeight: '500px' }}
      />

      {/* Statut du serveur de tuiles */}
      <div className="absolute top-4 left-4 bg-black bg-opacity-80 p-2 rounded border text-white text-xs">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            tileServerStatus === 'available' ? 'bg-green-500' :
            tileServerStatus === 'unavailable' ? 'bg-red-500' : 'bg-yellow-500'
          }`}></div>
          <span>
            {tileServerStatus === 'available' ? 'Tuiles vectorielles' :
             tileServerStatus === 'unavailable' ? 'Mode fallback' : 'Vérification...'}
          </span>
        </div>
      </div>

      {/* Légende des équipements */}
      <div className="absolute bottom-4 left-4 bg-black bg-opacity-90 p-3 rounded border text-white">
        <h4 className="text-sm font-bold mb-2 text-blue-400">Équipements C2-EW</h4>
        <div className="space-y-1">
          {Object.entries(EQUIPMENT_LAYERS).map(([type, config]) => (
            <div key={type} className="flex items-center space-x-2 text-xs">
              <span style={{ color: config.color }}>{config.icon}</span>
              <span>{config.name}</span>
              <span className="text-gray-400">({config.description})</span>
            </div>
          ))}
        </div>
      </div>

      {/* Informations sur la carte */}
      <div className="absolute top-4 right-4 bg-black bg-opacity-90 p-3 rounded border text-white">
        <h4 className="text-sm font-bold text-green-400">{MOROCCO_CONFIG.name}</h4>
        <p className="text-xs text-gray-300">
          {equipmentData.length} équipement(s) • Intégrité territoriale
        </p>
        {enableDrawing && (
          <p className="text-xs text-blue-300 mt-1">
            Outils de dessin et mesure activés
          </p>
        )}
      </div>
    </div>
  );
};

export default OfflineMap;

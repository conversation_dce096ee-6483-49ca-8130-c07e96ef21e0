{"name": "leaflet-measure", "version": "3.1.0", "description": "Coordinate, linear, and area measure tool for Leaflet maps", "main": "dist/leaflet-measure.js", "repository": {"type": "git", "url": "git://github.com/ljagis/leaflet-measure.git"}, "keywords": ["leaflet", "measure", "measurement", "control", "coordinate", "line", "length", "area", "polygon", "path"], "author": "LJA Engineering, Inc - GIS <<EMAIL>>", "license": "MIT", "scripts": {"lint": "eslint --ext js src/", "build": "webpack --config webpack.prod.js -p", "start:dev": "webpack-dev-server --open --config webpack.dev.js", "precommit": "lint-staged", "prepublishOnly": "yarn build", "ghpages": "yarn build && gh-pages -d dist"}, "lint-staged": {"*.js": ["eslint"], "*.{js,scss}": ["prettier --write", "git add"]}, "dependencies": {"@turf/area": "^5.1.5", "@turf/length": "^5.1.5", "lodash": "^4.17.5"}, "devDependencies": {"babel-core": "^6.26.0", "babel-loader": "^7.1.2", "babel-preset-env": "^1.6.1", "babel-runtime": "^6.26.0", "copy-webpack-plugin": "^4.4.1", "css-loader": "^0.28.9", "eslint": "^4.17.0", "eslint-plugin-import": "^2.8.0", "extract-text-webpack-plugin": "^3.0.2", "gh-pages": "^1.1.0", "glob": "^7.1.2", "html-loader": "^0.5.5", "husky": "^0.14.3", "i18n-webpack-plugin": "^1.0.0", "lint-staged": "^6.1.0", "node-sass": "^4.7.2", "prettier": "^1.10.2", "sass-loader": "^6.0.6", "style-loader": "^0.20.1", "webpack": "^3.10.0", "webpack-dev-server": "^2.11.1"}, "peerDependencies": {"leaflet": "^1.0.0"}}
# @turf/bbox

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## bbox

Takes a set of features, calculates the bbox of all input features, and returns a bounding box.

**Parameters**

-   `geojson` **[GeoJSON][1]** any GeoJSON object

**Examples**

```javascript
var line = turf.lineString([[-74, 40], [-78, 42], [-82, 35]]);
var bbox = turf.bbox(line);
var bboxPolygon = turf.bboxPolygon(bbox);

//addToMap
var addToMap = [line, bboxPolygon]
```

Returns **[BBox][2]** bbox extent in [minX, minY, maxX, maxY] order

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://tools.ietf.org/html/rfc7946#section-5

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/bbox
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
